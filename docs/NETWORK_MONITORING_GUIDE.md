# 🌐 Network Monitoring System Guide

> **Complete guide** for the simplified network monitoring system with clean architecture

## 📋 Table of Contents

1. [Overview](#-overview)
2. [Architecture](#-architecture)
3. [Implementation](#-implementation)
4. [Usage Examples](#-usage-examples)
5. [Configuration](#-configuration)
6. [Testing](#-testing)
7. [Troubleshooting](#-troubleshooting)

## 🎯 Overview

The Network Monitoring System provides real-time network connectivity monitoring with:

- **Simplified Architecture**: Core enum-based design without mapping complexity
- **Smart Detection**: Distinguishes between disconnected and unstable connections
- **Automatic UI**: Full-screen overlay for disconnected, SnackBar for reconnection
- **Global Integration**: Works across the entire app with NetworkAwareApp wrapper
- **Clean Architecture**: Follows dependency rules with proper layer separation
- **Internationalization**: Full i18n support for all user-facing messages

## 🏗️ Architecture

### **Layer Structure**

```
Core Layer (Shared)
├── NetworkConnectionStatus enum
└── Extension methods

Domain Layer (Business Logic)
├── NetworkMonitoringService interface
└── NetworkMonitoringConfig class

Data Layer (Implementation)
└── NetworkMonitoringServiceImpl

Presentation Layer (UI)
├── NetworkMonitorController (Riverpod)
├── NetworkStateLocalizer (i18n)
└── UI Components (Overlay, Notifications, App Wrapper)
```

### **Key Components**

| Component | Layer | Responsibility |
|-----------|-------|----------------|
| `NetworkConnectionStatus` | Core | Enum with connection states |
| `NetworkMonitoringService` | Domain | Business logic interface |
| `NetworkMonitoringServiceImpl` | Data | Stability tracking implementation |
| `NetworkMonitorController` | Presentation | Riverpod state management |
| `NetworkStateLocalizer` | Presentation | Localization helper |
| `NetworkWarningOverlay` | Presentation | Full-screen disconnection UI |
| `NetworkNotificationService` | Presentation | SnackBar notifications |
| `NetworkAwareApp` | Presentation | Global app wrapper |

## 🚀 Implementation

### **1. Core Enum (Shared)**

```dart
// lib/core/enums/network_connection_status.dart
enum NetworkConnectionStatus {
  connected,    // Normal internet connection
  disconnected, // No internet connection (shows overlay)
  unstable,     // Frequent disconnections (shows notifications)
  checking,     // Currently checking connection status
}

extension NetworkConnectionStatusExtension on NetworkConnectionStatus {
  bool get isConnected => this == NetworkConnectionStatus.connected;
  bool get isDisconnected => this == NetworkConnectionStatus.disconnected;
  bool get isUnstable => this == NetworkConnectionStatus.unstable;
  bool get isChecking => this == NetworkConnectionStatus.checking;
  
  String get statusIcon {
    switch (this) {
      case NetworkConnectionStatus.connected: return '✅';
      case NetworkConnectionStatus.disconnected: return '❌';
      case NetworkConnectionStatus.unstable: return '⚠️';
      case NetworkConnectionStatus.checking: return '🔄';
    }
  }
}
```

### **2. Domain Service (Business Logic)**

```dart
// lib/domain/services/network_monitoring_service.dart
abstract class NetworkMonitoringService {
  Stream<NetworkConnectionStatus> get connectionStatusStream;
  NetworkConnectionStatus get currentStatus;
  
  Future<void> initialize();
  Future<void> checkConnection();
  void forceConnected();
  void handleConnectivityChange(bool isConnected);
  void dispose();
}

class NetworkMonitoringConfig {
  final int unstableThreshold;           // 3 disconnections
  final Duration unstableWindow;         // in 2 minutes
  final Duration checkDelay;             // 2 seconds
  final Duration stabilizationDuration;  // 5 minutes
  
  const NetworkMonitoringConfig({
    this.unstableThreshold = 3,
    this.unstableWindow = const Duration(minutes: 2),
    this.checkDelay = const Duration(seconds: 2),
    this.stabilizationDuration = const Duration(minutes: 5),
  });
}
```

### **3. Presentation Controller**

```dart
// lib/presentation/controllers/network_monitor_controller.dart
@riverpod
class NetworkMonitorController extends _$NetworkMonitorController {
  late final NetworkMonitoringService _networkMonitoringService;
  StreamSubscription<NetworkConnectionStatus>? _stateSubscription;

  @override
  NetworkConnectionStatus build() {
    _networkMonitoringService = getIt<NetworkMonitoringService>();
    _initializeNetworkMonitoring();
    return _networkMonitoringService.currentStatus;
  }

  void _initializeNetworkMonitoring() {
    _networkMonitoringService.initialize();
    
    _stateSubscription = _networkMonitoringService.connectionStatusStream.listen(
      (newStatus) => state = newStatus,
    );
  }

  Future<void> checkConnection() => _networkMonitoringService.checkConnection();
  void forceConnected() => _networkMonitoringService.forceConnected();
}
```

## 📱 Usage Examples

### **1. Basic Status Checking**

```dart
class MyWidget extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final networkStatus = ref.watch(networkMonitorControllerProvider);
    
    // Simple boolean check
    if (networkStatus.isConnected) {
      return OnlineFeatures();
    } else {
      return OfflineContent();
    }
  }
}
```

### **2. Switch Statement for All States**

```dart
class NetworkAwareWidget extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final networkStatus = ref.watch(networkMonitorControllerProvider);
    
    switch (networkStatus) {
      case NetworkConnectionStatus.connected:
        return OnlineContent();
      case NetworkConnectionStatus.disconnected:
        return OfflineMessage();
      case NetworkConnectionStatus.unstable:
        return UnstableWarning();
      case NetworkConnectionStatus.checking:
        return LoadingIndicator();
    }
  }
}
```

### **3. Localized Messages**

```dart
class NetworkStatusDisplay extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final networkStatus = ref.watch(networkMonitorControllerProvider);
    
    return Column(
      children: [
        Text(NetworkStateLocalizer.getStatusMessage(context, networkStatus)),
        Text(networkStatus.statusIcon),
        if (networkStatus.isDisconnected)
          ElevatedButton(
            onPressed: () => ref.read(networkMonitorControllerProvider.notifier).checkConnection(),
            child: Text(NetworkStateLocalizer.getRetryButtonText(context, networkStatus)),
          ),
      ],
    );
  }
}
```

### **4. Manual Network Operations**

```dart
class NetworkControls extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller = ref.read(networkMonitorControllerProvider.notifier);
    
    return Column(
      children: [
        ElevatedButton(
          onPressed: () => controller.checkConnection(),
          child: Text('Check Connection'),
        ),
        ElevatedButton(
          onPressed: () => controller.forceConnected(),
          child: Text('Force Connected (Emergency)'),
        ),
      ],
    );
  }
}
```

## ⚙️ Configuration

### **Network Detection Settings**

```dart
// Customize detection behavior
class NetworkMonitoringConfig {
  static const NetworkMonitoringConfig defaultConfig = NetworkMonitoringConfig(
    unstableThreshold: 3,                           // 3 disconnections
    unstableWindow: Duration(minutes: 2),           // in 2 minutes
    checkDelay: Duration(seconds: 2),               // Check delay
    stabilizationDuration: Duration(minutes: 5),   // Clear unstable after
  );
  
  // Development config (more sensitive)
  static const NetworkMonitoringConfig developmentConfig = NetworkMonitoringConfig(
    unstableThreshold: 2,                           // 2 disconnections
    unstableWindow: Duration(minutes: 1),           // in 1 minute
    checkDelay: Duration(seconds: 1),               // Faster check
    stabilizationDuration: Duration(minutes: 2),   // Faster recovery
  );
}
```

### **UI Customization**

```dart
// Custom overlay colors and behavior
NetworkWarningOverlay(
  backgroundColor: Colors.red,
  opacity: 0.9,
  messageStyle: TextStyle(fontSize: 18),
  onRetry: () => customRetryLogic(),
)

// Custom app wrapper configuration
MaterialApp.router(
  builder: (context, child) => NetworkAwareAppBuilder.buildCustom(
    context,
    child,
    showOverlay: true,        // Show overlay for disconnected
    showNotifications: true,  // Show SnackBar notifications
    onNetworkRetry: () => customRetryHandler(),
  ),
)
```

## 🧪 Testing

### **Unit Tests**

```dart
test('NetworkConnectionStatus should have correct properties', () {
  expect(NetworkConnectionStatus.connected.isConnected, true);
  expect(NetworkConnectionStatus.disconnected.isDisconnected, true);
  expect(NetworkConnectionStatus.unstable.isUnstable, true);
  expect(NetworkConnectionStatus.checking.isChecking, true);
  
  expect(NetworkConnectionStatus.connected.statusIcon, '✅');
  expect(NetworkConnectionStatus.disconnected.statusIcon, '❌');
});
```

### **Integration Tests**

```dart
testWidgets('Network overlay shows on disconnection', (tester) async {
  await tester.pumpWidget(MyApp());
  
  // Simulate disconnection through the domain service
  // (Implementation depends on your testing setup)
  
  await tester.pump();
  
  expect(find.byType(NetworkWarningOverlay), findsOneWidget);
  expect(find.text('Mất kết nối mạng'), findsOneWidget);
});
```

### **Manual Testing**

```dart
// Debug tools for manual testing
final networkStatus = ref.read(networkMonitorControllerProvider);
print('Current status: $networkStatus');
print('Is connected: ${networkStatus.isConnected}');
print('Status icon: ${networkStatus.statusIcon}');

// Force different states for testing
ref.read(networkMonitorControllerProvider.notifier).forceConnected();
await ref.read(networkMonitorControllerProvider.notifier).checkConnection();
```

## 🔧 Troubleshooting

### **Common Issues**

1. **Overlay not showing**: Check if NetworkAwareApp is properly wrapped around MaterialApp
2. **Notifications not appearing**: Verify ScaffoldMessenger is initialized
3. **Status not updating**: Ensure connectivity_plus permissions are granted
4. **Localization not working**: Check if S.of(context) is available

### **Debug Information**

```dart
// Enable detailed logging
AppLogger.info('Network status changed', data: {
  'previous': previousStatus.toString(),
  'current': currentStatus.toString(),
  'timestamp': DateTime.now().toIso8601String(),
});
```

### **Performance Monitoring**

- **CPU Usage**: Minimal - only listens to connectivity stream
- **Memory Usage**: Low - simple enum-based state management
- **Battery Impact**: Optimized - uses system connectivity APIs
- **Network Overhead**: None - no additional network calls

## 📊 Benefits

### **Simplified Architecture**
- ✅ **No mapping complexity**: Direct enum usage
- ✅ **Better performance**: Enum operations faster than Freezed classes
- ✅ **Easier maintenance**: Single source of truth
- ✅ **Clean architecture**: Core enum shared across layers

### **User Experience**
- ✅ **Immediate feedback**: Real-time network status updates
- ✅ **Non-intrusive**: Overlay only for disconnected, notifications for others
- ✅ **Emergency bypass**: Continue button for critical situations
- ✅ **Localized**: Full internationalization support

### **Developer Experience**
- ✅ **Simple API**: Easy to use and understand
- ✅ **Type safety**: Enum-based with extension methods
- ✅ **Testable**: Clear separation of concerns
- ✅ **Extensible**: Easy to add new features
