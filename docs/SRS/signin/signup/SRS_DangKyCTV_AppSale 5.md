SRS – Đăng ký tài khoản trên App Sale
1. <PERSON><PERSON><PERSON><PERSON> thiệu
1.1 M<PERSON><PERSON> đích
Tài liệu này mô tả chi tiết yêu cầu hệ thống cho chức năng đăng ký tài khoản Cộng Tác <PERSON> (CTV) trên ứng dụng App Sale của KienlongBank. Chức năng cho phép người bên ngoài hệ thống thực hiện đăng ký trực tuyến, xác minh giấy tờ và gửi yêu cầu phê duyệt thông qua luồng xác thực.

1.2 Phạm vi
Tính năng này là một phần của hệ thống App Sale – nền tảng phục vụ đội ngũ bán hàng và cộng tác viên giới thiệu sản phẩm vay.

1.3 Định nghĩa và từ viết tắt
CTV: Cộng Tác Viên
KLB: KienlongBank
OCR: Nhận diện ký tự quang học (Optical Character Recognition)
UI/UX: Giao diện người dùng / Trải nghiệm người dùng
1.4 Vai trò sử dụng
Người dùng ngoài hệ thống có nhu cầu làm CTV
Admin/ GĐKD thực hiện phê duyệt hồ sơ từ app/ web sale
2. Mô tả màn hình UI/UX
Từ màn hình Đăng nhập, chọn Đăng ký tài khoản => Hiển thị màn hình chọn loại tài khoản đăng ký mở

2.1 Màn hình chọn loại tài khoản
Button: Nút quay trở lại màn đăng nhập image: Logo "KienlongBank" Text: Sologan "Không vốn, không rủi ro – vẫn có thu nhập!" Button: 2 nút chức năng - Tài khoản Cộng tác viên - Tài khoản Cán bộ bán hàng KienlongBank

Nếu chọn đăng ký tài khoản CTV chuyển sang màn 2.2

2.2 Màn hình Giới thiệu Cộng tác viên
Button: Nút quay trở lại màn chọn loại tài khoản Lable: tiêu đề màn hình "Giới thiệu" Text: Đoạn nội dụng text giới thiệu về nghiệp vụ lợi ích làm CTV vay trả góp ngày Button: Đăng ký

Màn hình hiển thị nội dung sau:
Vay trả góp ngày - Vay Trả góp ngày là sản phẩm cho vay theo hình thức trả góp ngày (gốc, lãi) nhằm phục vụ nhu cầu đời sống và hoạt động kinh doanh, hoạt động khác. - Khi thanh toán bằng thẻ, nếu số tiền giao dịch vượt quá số dư tài khoản thanh toán gắn với thẻ, ngân hàng sẽ tự động cấp khoản vay thấu chi để thanh toán cho giao dịch đó. - Phù hợp với cá nhân có nhu cầu chi tiêu đột xuất, ngắn hạn; doanh nghiệp nhỏ và cá nhân kinh doanh; khách hàng có thu nhập ổn định và lịch sử tín dụng tốt

Lợi ích khi là CTV - Thời gian làm việc tự do/linh hoạt. - Được nhận phí dịch vụ (PDV) hàng tháng định kỳ. - Thu nhập không giới hạn. - Được cấp phát đồng phục làm việc. - Được Kienlongbank mua bảo hiểm sức khỏe, Bảo hiểm tai nạn khi thỏa điều kiện về dư nợ. - Được chi thưởng các dịp lễ 30/04, Quốc khánh 02/9, ...

Các bước để trở thành CTV vay trả góp ngày - Là công dân Việt Nam có năng lực pháp luật, năng lực hành vi dân sự và chịu trách nhiệm dân sự theo quy định của pháp luật. - Lý lịch, nhân thân tốt; Không tiền án, tiền sự. - Tuổi từ 18 đến 60 tuổi, tốt nghiệp THCS trở lên. - Có đủ sức khoẻ, tinh thần minh mẫn. - Có tài sản bảo đảm tối thiểu là 150 triệu đồng và cam kết thực hiện đúng quy định về dư nợ bình quân tối thiểu của Kienlongbank. - Am hiểu địa bàn nơi làm việc. - Không đồng thời làm việc cho tổ chức tín dụng, công ty tài chính khác.

2.3 Màn hình Hướng dẫn xác thực giấy tờ
Button: Nút quay trở lại màn Giới thiệu Tiêu đề lable: "Hướng dẫn xác thực giấy tờ " image: ảnh CCCD minh hoạ Text: - Giấy tờ còn hạn sử dụng. Là hình gốc, không scan và photocopy. - Chụp trong môi trường đủ ánh sáng. - Đảm bảo ảnh rõ nét, không bị mờ loá.

Text: Tránh sử dụng image 1: ảnh minh hoạ CCCD có dấu X(không nên) Text: không chụp quá mờ

image 2: ảnh minh hoạ CCCD có dấu X(không nên) Text: Không mất góc

image 3: ảnh minh hoạ CCCD có dấu X(không nên) Text: Không chụp loá sáng Button: Tôi đã hiểu

2.4 Màn hình cung cấp giấy tờ tùy thân
Button: Nút quay trở lại màn Hướng dẫn xác thực giấy tờ Tiêu đề: “Đăng ký” Text: Cung cấp giấy tờ tuỳ thân Text hướng dẫn: “Vui lòng chọn loại giấy tờ tùy thân để xác thực thông tin” Dropdown : CCCD / Thẻ Căn Cước. Mặc định chọn CCCD - Khung hiển thị và upload ảnh mặt trước giấy tờ + icon máy ảnh - Khung hiển thị và upload ảnh mặt sau giấy tờ + icon máy ảnh

Button: Tiếp tục, disable cho đến khi có đủ 2 mặt ảnh

2.5 Màn hình chọn loại tải lên giấy tờ
Xuất hiện khi click chọn một khung ảnh giấy tờ Hiển thị dưới dạng popup: - Button: Chụp ảnh - Button: chọn ảnh trong thư viện - Button phụ: Huỷ bỏ

2.6 Màn hình trước khi chụp ảnh giấy tờ mặt trước
Button: Nút quay trở lại màn cung cấp giấy tờ tùy thân Tiêu đề: “Chụp mặt trước” - Khung chụp trống

Text: Vui lòng đặt giấy tờ vào trong khung để xác thực Button: click để chụp ảnh

2.7 Màn hình sau khi chụp ảnh giấy tờ mặt trước
Button: Nút quay trở lại màn cung cấp giấy tờ tùy thân Tiêu đề: “Chụp mặt trước” - Khung chụp hiển thị ảnh đã chụp

Text: Kiểm tra mặt trước GTTT Button: Chụp lại Button: Lấy ảnh này

2.8 Màn hình trước khi chụp ảnh giấy tờ mặt sau
Button: Nút quay trở lại màn cung cấp giấy tờ tùy thân Tiêu đề: “Chụp mặt sau - Khung chụp trống

Text: Vui lòng đặt giấy tờ vào trong khung để xác thực Button: click để chụp ảnh

2.9 Màn hình sau khi chụp ảnh giấy tờ mặt sau
Button: Nút quay trở lại màn cung cấp giấy tờ tùy thân Tiêu đề: “Chụp mặt sau - Khung chụp hiển thị ảnh đã chụp

Text: Kiểm tra mặt sau GTTT Button: Chụp lại Button: Lấy ảnh này

2.10 Màn hình quét mã QR CCCD
Button: Nút quay trở lại màn cung cấp giấy tờ tùy thân Button: Bỏ qua Text: “Vui lòng đưa QR trên CCCD vào khung hình” - Camera view ở giữa màn hình - Logo KienlongBank

Button: bật/tắt đèn flash Button: tải ảnh QR từ thư viện

2.11 Màn hình xác nhận thông tin cá nhân
Các field read-only hiển thị thông tin trích xuất từ giấy tờ, các trường có thể sửa:
Họ và tên (Textbox): chuỗi ký tự không chứa số, độ dài 3–50 ký tự.
Số giấy tờ (Textbox): số định danh cá nhân, định dạng 9 hoặc 12 chữ số.
Địa chỉ cư trú (Textbox): text hiển thị từ OCR/QR, tối đa 255 ký tự.
Các trường người dùng nhập (DOD – Definition of Done):
Số điện thoại (Textbox): 10 chữ số, bắt đầu bằng số 0, chỉ nhận ký tự số.
Chức danh (read-only): “CTV đăng ký mới”
Tỉnh/Thành phố (Dropdown): danh sách tỉnh có chi nhánh KLB. Bắt buộc nếu chọn CTV đăng ký mới.
Chi nhánh đăng ký (Dropdown): Chi nhánh thuộc tỉnh thành phố đã chọn
Địa chỉ chi nhánh (read-only): Hiển thị địa chỉ sau khi đã chọn chi nhánh
Mã người giới thiệu (nếu có)(Textbox): nhập mã người giới thiệu
Xác nhận (Button)
Huỷ (button)
2.7 Màn hình thông báo sau gửi thành công
Text: “Khởi tạo đăng ký thành công” Text: KienlongBank sẽ liên hệ với bạn trong thời gian sớm nhất để thông báo kết quả! Text: thông tin đăng ký Họ tên (Textbox): Họ tên người đăng ký Chi nhánh đăng ký (Textbox) Chức danh(textbox)

3. Luồng chức năng
3.1 Mô tả luồng xử lý chi tiết
Từ màn hình đăng nhập → người dùng chọn “Đăng ký tài khoản” → điều hướng đến màn hình Chọn loại tài khoản.
Tại màn hình Chọn loại tài khoản:
Chọn Đăng ký tài khoản CTV → điều hướng đến màn hình Giới thiệu.
(Nếu chọn “Đăng ký tài khoản Cán bộ bán hàng KienlongBank” → quy trình khác, không thuộc phạm vi tài liệu này.)
Tại màn hình Giới thiệu:
Nhấn Đăng ký → sang màn hình Hướng dẫn xác thực giấy tờ
Tại màn hình Hướng dẫn xác thực giấy tờ
Nhấn Tôi đã hiểu -> sang màn Cung cấp thông tin giấy tờ
Tại màn hình Cung cấp giấy tờ tùy thân:
Chọn loại giấy tờ (CCCD/Thẻ căn cước), upload đủ 2 mặt → nút Tiếp tục bật
Nhấn Tiếp tục:
Nếu là CCCD → sang màn hình Quét mã QR
Nếu là Thẻ Căn Cước → sang màn hình Xác nhận thông tin cá nhân
Tại màn hình Quét mã QR:
Người dùng đưa CCCD vào camera hoặc tải ảnh QR lên
Hoặc chọn Bỏ qua để dùng OCR
Hệ thống đọc và đổ thông tin → sang màn hình Xác nhận thông tin cá nhân
Tại màn hình Xác nhận thông tin cá nhân:
Hiển thị thông tin đã quét, người dùng nhập bổ sung
Khi nhập đủ và đúng định dạng → nút Xác nhận bật
Nhấn Xác nhận → gửi yêu cầu
Hệ thống gửi:
Thông báo + email đến GĐ/PGĐ đơn vị để duyệt
Gửi email xác nhận cho người đăng ký
Cấu trúc email:
Người nhận: Giám đốc/Phó giám đốc phụ trách đơn vị đó
CC: Email người đăng ký
Tiêu đề: Tiếp nhận yêu cầu đăng ký làm CTV Sale App
Nội dung: Bạn có 1 yêu cầu đăng ký làm CTV. Họ và Tên: Nguyễn Van A CCCD: 053873467632 Người giới thiệu: Nguyễn Thị L – 352145 (Họ và tên - Mã người giới thiệu) Sau khi hoàn thành quy trình tuyển dụng. Vui lòng truy cập vào app hoặc link “abc” để phê duyệt cho CTV vào App Sale.
- Hiển thị màn hình xác nhận thông tin khởi tạo khoản vay thành công
4. Yêu cầu phi chức năng
Giao diện tuân thủ chuẩn mobile-first
Sử dụng tone màu thương hiệu: xanh lá – trắng – xám nhạt
Giao diện bo góc, thân thiện với người dùng phổ thông
Hỗ trợ tích hợp OCR và quét QR từ CCCD
5. Ràng buộc nghiệp vụ
Mã	Quy tắc
BR01	Không tick checkbox → không được Tiếp tục
BR02	Phải upload đầy đủ 2 mặt giấy tờ
BR03	Nếu giấy tờ là CCCD → có tùy chọn quét QR
BR04	Nếu là CTV đang công tác → bắt buộc nhập email KLB
BR05	Số điện thoại: đúng 10 số, bắt đầu bằng 0
BR06	Tỉnh/thành không có CN/PGD KLB → không hiển thị trong droplist
BR07	Nút Xác nhận chỉ enable khi nhập đủ các trường bắt buộc
BR08	Nếu chọn loại tài khoản nội bộ → chuyển hướng sang quy trình riêng
BR09	Phải tick chấp nhận chính sách CTV thì mới được Tiếp tục
BR10	Phải chụp/upload ảnh mặt trước và mặt sau giấy tờ tùy thân mới được Tiếp tục
BR11	Nếu loại giấy tờ là CCCD thì bắt buộc quét QR hoặc Bỏ qua (không được bỏ trống)
BR12	Chỉ hiển thị tỉnh/huyện nếu là CTV đăng ký mới
BR13	Email KLB không được nhập domain khác ngoài @kienlongbank.com
BR14	Người giới thiệu (nếu có) phải đúng định dạng mã hệ thống
BR15	Mỗi số giấy tờ chỉ được đăng ký 1 lần – không trùng trong trạng thái đang xử lý
6. Xử lý ngoại lệ
Mã lỗi	Tình huống	Phản hồi hệ thống
EX001	Ảnh giấy tờ mờ/thiếu góc	Cảnh báo: “Ảnh không hợp lệ”
EX002	Chưa nhập đủ trường bắt buộc	Highlight đỏ, disable nút gửi
EX003	Tỉnh không có chi nhánh	Ẩn khỏi droplist tỉnh/thành
EX004	Email sai định dạng	Cảnh báo tại trường Email
EX005	Quét mã QR không thành công	Cảnh báo: “Không đọc được mã QR. Vui lòng thử lại hoặc chọn Bỏ qua để dùng OCR.”
EX006	Upload ảnh lỗi (định dạng không hỗ trợ)	Cảnh báo: “Tệp ảnh không hợp lệ. Vui lòng chọn định dạng JPG hoặc PNG.”
EX007	Người giới thiệu không tồn tại hoặc sai mã	Cảnh báo: “Mã CTV người giới thiệu không hợp lệ.”
EX008	Mạng yếu, không gửi được yêu cầu	Thông báo: “Kết nối mạng không ổn định. Vui lòng thử lại sau.”
EX009	Trùng số giấy tờ với hồ sơ đang xử lý	Thông báo: “Thông tin đã tồn tại trên hệ thống. Vui lòng liên hệ hỗ trợ.”
EX010	Không chọn loại tài khoản	Cảnh báo: “Vui lòng chọn loại tài khoản để tiếp tục”
EX011	Người dùng không cấp quyền camera	Cảnh báo hướng dẫn mở lại quyền trong cài đặt thiết bị
EX012	Ảnh CCCD bị lóa, lệch góc	Cảnh báo: “Ảnh chưa đủ rõ ràng. Vui lòng chụp lại”
EX013	Upload file quá dung lượng quy định	Cảnh báo: “Tệp ảnh vượt quá dung lượng cho phép (tối đa 5MB)”