import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sales_app/core/enums/document_type.dart';
import 'package:sales_app/core/enums/loan_step.dart';

part 'create_loan_controller.freezed.dart';

@freezed
class CreateLoanState with _$CreateLoanState {
  const factory CreateLoanState({
    @Default(LoanStep.identityDocument) LoanStep currentStep,
    @Default(null) ({
      DocumentType documentType,
      String frontImagePath,
      String? backImagePath,
    })? step1Data,
    @Default(false) bool step2Data,
    @Default(false) bool step3Data,
    @Default(false) bool step4Data,
    @Default(false) bool step5Data,
    @Default(false) bool step6Data,
    @Default(false) bool step7Data,
    @Default(false) bool step8Data,
    @Default(false) bool step9Data,
    @Default(false) bool step10Data,
  }) = _CreateLoanState;
}

class CreateLoanController extends StateNotifier<CreateLoanState> {
  CreateLoanController() : super(const CreateLoanState());

  static int get totalSteps => LoanStep.totalSteps;

  // Kiểm tra điều kiện tiếp tục cho từng bước
  bool isContinueEnabled() {
    switch (state.currentStep) {
      case LoanStep.identityDocument:
        if (state.step1Data == null) return false;
        final data = state.step1Data!;
        if (data.documentType == DocumentType.passport) {
          return data.frontImagePath.isNotEmpty;
        }
        return data.frontImagePath.isNotEmpty &&
               data.backImagePath != null &&
               data.backImagePath!.isNotEmpty;
      case LoanStep.borrowerInfo:
        return state.step2Data;
      case LoanStep.coBorrowerDocument:
        return state.step3Data;
      case LoanStep.coBorrowerInfo:
        return state.step4Data;
      case LoanStep.loanRequest:
        return state.step5Data;
      case LoanStep.financialInfo:
        return state.step6Data;
      case LoanStep.collateralInfo:
        return state.step7Data;
      case LoanStep.collateralDetail:
        return state.step8Data;
      case LoanStep.documentList:
        return state.step9Data;
      case LoanStep.loanConfirmation:
        return state.step10Data;
      case LoanStep.success:
        return false; // Không có nút tiếp tục ở bước cuối
    }
  }

  // Xử lý tiếp tục cho từng bước
  void handleContinue() {
    final nextStep = state.currentStep.next;
    if (nextStep != null && isContinueEnabled()) {
      state = state.copyWith(currentStep: nextStep);
    }
  }

  // Cập nhật dữ liệu cho từng bước
  void updateStep1Data({
    required DocumentType documentType,
    required String frontImagePath,
    String? backImagePath,
  }) {
    state = state.copyWith(
      step1Data: (
        documentType: documentType,
        frontImagePath: frontImagePath,
        backImagePath: backImagePath,
      ),
    );
  }

  void updateStep2Data(bool value) {
    state = state.copyWith(step2Data: value);
  }

  void updateStep3Data(bool value) {
    state = state.copyWith(step3Data: value);
  }

  void updateStep4Data(bool value) {
    state = state.copyWith(step4Data: value);
  }

  void updateStep5Data(bool value) {
    state = state.copyWith(step5Data: value);
  }

  void updateStep6Data(bool value) {
    state = state.copyWith(step6Data: value);
  }

  void updateStep7Data(bool value) {
    state = state.copyWith(step7Data: value);
  }

  void updateStep8Data(bool value) {
    state = state.copyWith(step8Data: value);
  }

  void updateStep9Data(bool value) {
    state = state.copyWith(step9Data: value);
  }

  void updateStep10Data(bool value) {
    state = state.copyWith(step10Data: value);
  }

  // Quay lại bước trước
  void goBack() {
    final previousStep = state.currentStep.previous;
    if (previousStep != null) {
      state = state.copyWith(currentStep: previousStep);
    }
  }
}

final createLoanControllerProvider =
    AutoDisposeStateNotifierProvider<CreateLoanController, CreateLoanState>((ref) {
  return CreateLoanController();
});