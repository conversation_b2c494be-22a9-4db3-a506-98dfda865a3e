import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sales_app/core/enums/document_type.dart';
import 'package:sales_app/generated/l10n.dart';

part 'create_loan_controller.freezed.dart';

@freezed
class CreateLoanState with _$CreateLoanState {
  const factory CreateLoanState({
    @Default(1) int currentStep,
    @Default(null) ({
      DocumentType documentType,
      String frontImagePath,
      String? backImagePath,
    })? step1Data,
    @Default(false) bool step2Data,
    @Default(false) bool step3Data,
    @Default(false) bool step4Data,
    @Default(false) bool step5Data,
    @Default(false) bool step6Data,
    @Default(false) bool step7Data,
    @Default(false) bool step8Data,
    @Default(false) bool step9Data,
    @Default(false) bool step10Data,
  }) = _CreateLoanState;
}

class CreateLoanController extends StateNotifier<CreateLoanState> {
  CreateLoanController() : super(const CreateLoanState());

  static const int totalSteps = 11;

  // Map các bước với tiêu đề tương ứng
  static const Map<int, String> stepTitles = {
    1: 'Cung cấp giấy tờ tùy thân',
    2: 'Xác nhận thông tin người vay chính',
    3: 'Cung cấp giấy tờ tùy thân (người đồng vay)',
    4: 'Xác nhận thông tin người đồng vay',
    5: 'Cung cấp thông tin đề nghị vay vốn',
    6: 'Cung cấp thông tin tình hình tài chính',
    7: 'Cung cấp thông tin tài sản bảo đảm',
    8: 'Chi tiết thông tin tài sản bảo đảm',
    9: 'Cung cấp danh mục chứng từ',
    10: 'Xác nhận thông tin khoản vay',
    11: 'Khởi tạo khoản vay thành công',
  };

  // Kiểm tra điều kiện tiếp tục cho từng bước
  bool isContinueEnabled() {
    switch (state.currentStep) {
      case 1:
        if (state.step1Data == null) return false;
        final data = state.step1Data!;
        if (data.documentType == DocumentType.passport) {
          return data.frontImagePath.isNotEmpty;
        }
        return data.frontImagePath.isNotEmpty &&
               data.backImagePath != null &&
               data.backImagePath!.isNotEmpty;
      case 2:
        return state.step2Data;
      case 3:
        return state.step3Data;
      case 4:
        return state.step4Data;
      case 5:
        return state.step5Data;
      case 6:
        return state.step6Data;
      case 7:
        return state.step7Data;
      case 8:
        return state.step8Data;
      case 9:
        return state.step9Data;
      case 10:
        return state.step10Data;
      case 11:
        return false; // Không có nút tiếp tục ở bước cuối
      default:
        return false;
    }
  }

  // Xử lý tiếp tục cho từng bước
  void handleContinue() {
    switch (state.currentStep) {
      case 1:
        if (state.step1Data != null) {
          state = state.copyWith(currentStep: 2);
        }
        break;
      case 2:
        if (state.step2Data) {
          state = state.copyWith(currentStep: 3);
        }
        break;
      case 3:
        if (state.step3Data) {
          state = state.copyWith(currentStep: 4);
        }
        break;
      case 4:
        if (state.step4Data) {
          state = state.copyWith(currentStep: 5);
        }
        break;
      case 5:
        if (state.step5Data) {
          state = state.copyWith(currentStep: 6);
        }
        break;
      case 6:
        if (state.step6Data) {
          state = state.copyWith(currentStep: 7);
        }
        break;
      case 7:
        if (state.step7Data) {
          state = state.copyWith(currentStep: 8);
        }
        break;
      case 8:
        if (state.step8Data) {
          state = state.copyWith(currentStep: 9);
        }
        break;
      case 9:
        if (state.step9Data) {
          state = state.copyWith(currentStep: 10);
        }
        break;
      case 10:
        if (state.step10Data) {
          state = state.copyWith(currentStep: 11);
        }
        break;
      default:
        break;
    }
  }

  // Cập nhật dữ liệu cho từng bước
  void updateStep1Data({
    required DocumentType documentType,
    required String frontImagePath,
    String? backImagePath,
  }) {
    state = state.copyWith(
      step1Data: (
        documentType: documentType,
        frontImagePath: frontImagePath,
        backImagePath: backImagePath,
      ),
    );
  }

  void updateStep2Data(bool value) {
    state = state.copyWith(step2Data: value);
  }

  void updateStep3Data(bool value) {
    state = state.copyWith(step3Data: value);
  }

  void updateStep4Data(bool value) {
    state = state.copyWith(step4Data: value);
  }

  void updateStep5Data(bool value) {
    state = state.copyWith(step5Data: value);
  }

  void updateStep6Data(bool value) {
    state = state.copyWith(step6Data: value);
  }

  void updateStep7Data(bool value) {
    state = state.copyWith(step7Data: value);
  }

  void updateStep8Data(bool value) {
    state = state.copyWith(step8Data: value);
  }

  void updateStep9Data(bool value) {
    state = state.copyWith(step9Data: value);
  }

  void updateStep10Data(bool value) {
    state = state.copyWith(step10Data: value);
  }

  // Quay lại bước trước
  void goBack() {
    if (state.currentStep > 1) {
      state = state.copyWith(currentStep: state.currentStep - 1);
    }
  }
}

final createLoanControllerProvider =
    StateNotifierProvider<CreateLoanController, CreateLoanState>((ref) {
  return CreateLoanController();
});