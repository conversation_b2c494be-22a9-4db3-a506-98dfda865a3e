// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'create_loan_controller.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$CreateLoanState {
  int get currentStep => throw _privateConstructorUsedError;
  ({String? backImagePath, DocumentType documentType, String frontImagePath})?
      get step1Data => throw _privateConstructorUsedError;
  bool get step2Data => throw _privateConstructorUsedError;
  bool get step3Data => throw _privateConstructorUsedError;
  bool get step4Data => throw _privateConstructorUsedError;
  bool get step5Data => throw _privateConstructorUsedError;
  bool get step6Data => throw _privateConstructorUsedError;
  bool get step7Data => throw _privateConstructorUsedError;
  bool get step8Data => throw _privateConstructorUsedError;
  bool get step9Data => throw _privateConstructorUsedError;
  bool get step10Data => throw _privateConstructorUsedError;

  /// Create a copy of CreateLoanState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CreateLoanStateCopyWith<CreateLoanState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreateLoanStateCopyWith<$Res> {
  factory $CreateLoanStateCopyWith(
          CreateLoanState value, $Res Function(CreateLoanState) then) =
      _$CreateLoanStateCopyWithImpl<$Res, CreateLoanState>;
  @useResult
  $Res call(
      {int currentStep,
      ({
        String? backImagePath,
        DocumentType documentType,
        String frontImagePath
      })? step1Data,
      bool step2Data,
      bool step3Data,
      bool step4Data,
      bool step5Data,
      bool step6Data,
      bool step7Data,
      bool step8Data,
      bool step9Data,
      bool step10Data});
}

/// @nodoc
class _$CreateLoanStateCopyWithImpl<$Res, $Val extends CreateLoanState>
    implements $CreateLoanStateCopyWith<$Res> {
  _$CreateLoanStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CreateLoanState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentStep = null,
    Object? step1Data = freezed,
    Object? step2Data = null,
    Object? step3Data = null,
    Object? step4Data = null,
    Object? step5Data = null,
    Object? step6Data = null,
    Object? step7Data = null,
    Object? step8Data = null,
    Object? step9Data = null,
    Object? step10Data = null,
  }) {
    return _then(_value.copyWith(
      currentStep: null == currentStep
          ? _value.currentStep
          : currentStep // ignore: cast_nullable_to_non_nullable
              as int,
      step1Data: freezed == step1Data
          ? _value.step1Data
          : step1Data // ignore: cast_nullable_to_non_nullable
              as ({
              String? backImagePath,
              DocumentType documentType,
              String frontImagePath
            })?,
      step2Data: null == step2Data
          ? _value.step2Data
          : step2Data // ignore: cast_nullable_to_non_nullable
              as bool,
      step3Data: null == step3Data
          ? _value.step3Data
          : step3Data // ignore: cast_nullable_to_non_nullable
              as bool,
      step4Data: null == step4Data
          ? _value.step4Data
          : step4Data // ignore: cast_nullable_to_non_nullable
              as bool,
      step5Data: null == step5Data
          ? _value.step5Data
          : step5Data // ignore: cast_nullable_to_non_nullable
              as bool,
      step6Data: null == step6Data
          ? _value.step6Data
          : step6Data // ignore: cast_nullable_to_non_nullable
              as bool,
      step7Data: null == step7Data
          ? _value.step7Data
          : step7Data // ignore: cast_nullable_to_non_nullable
              as bool,
      step8Data: null == step8Data
          ? _value.step8Data
          : step8Data // ignore: cast_nullable_to_non_nullable
              as bool,
      step9Data: null == step9Data
          ? _value.step9Data
          : step9Data // ignore: cast_nullable_to_non_nullable
              as bool,
      step10Data: null == step10Data
          ? _value.step10Data
          : step10Data // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CreateLoanStateImplCopyWith<$Res>
    implements $CreateLoanStateCopyWith<$Res> {
  factory _$$CreateLoanStateImplCopyWith(_$CreateLoanStateImpl value,
          $Res Function(_$CreateLoanStateImpl) then) =
      __$$CreateLoanStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int currentStep,
      ({
        String? backImagePath,
        DocumentType documentType,
        String frontImagePath
      })? step1Data,
      bool step2Data,
      bool step3Data,
      bool step4Data,
      bool step5Data,
      bool step6Data,
      bool step7Data,
      bool step8Data,
      bool step9Data,
      bool step10Data});
}

/// @nodoc
class __$$CreateLoanStateImplCopyWithImpl<$Res>
    extends _$CreateLoanStateCopyWithImpl<$Res, _$CreateLoanStateImpl>
    implements _$$CreateLoanStateImplCopyWith<$Res> {
  __$$CreateLoanStateImplCopyWithImpl(
      _$CreateLoanStateImpl _value, $Res Function(_$CreateLoanStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreateLoanState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentStep = null,
    Object? step1Data = freezed,
    Object? step2Data = null,
    Object? step3Data = null,
    Object? step4Data = null,
    Object? step5Data = null,
    Object? step6Data = null,
    Object? step7Data = null,
    Object? step8Data = null,
    Object? step9Data = null,
    Object? step10Data = null,
  }) {
    return _then(_$CreateLoanStateImpl(
      currentStep: null == currentStep
          ? _value.currentStep
          : currentStep // ignore: cast_nullable_to_non_nullable
              as int,
      step1Data: freezed == step1Data
          ? _value.step1Data
          : step1Data // ignore: cast_nullable_to_non_nullable
              as ({
              String? backImagePath,
              DocumentType documentType,
              String frontImagePath
            })?,
      step2Data: null == step2Data
          ? _value.step2Data
          : step2Data // ignore: cast_nullable_to_non_nullable
              as bool,
      step3Data: null == step3Data
          ? _value.step3Data
          : step3Data // ignore: cast_nullable_to_non_nullable
              as bool,
      step4Data: null == step4Data
          ? _value.step4Data
          : step4Data // ignore: cast_nullable_to_non_nullable
              as bool,
      step5Data: null == step5Data
          ? _value.step5Data
          : step5Data // ignore: cast_nullable_to_non_nullable
              as bool,
      step6Data: null == step6Data
          ? _value.step6Data
          : step6Data // ignore: cast_nullable_to_non_nullable
              as bool,
      step7Data: null == step7Data
          ? _value.step7Data
          : step7Data // ignore: cast_nullable_to_non_nullable
              as bool,
      step8Data: null == step8Data
          ? _value.step8Data
          : step8Data // ignore: cast_nullable_to_non_nullable
              as bool,
      step9Data: null == step9Data
          ? _value.step9Data
          : step9Data // ignore: cast_nullable_to_non_nullable
              as bool,
      step10Data: null == step10Data
          ? _value.step10Data
          : step10Data // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$CreateLoanStateImpl implements _CreateLoanState {
  const _$CreateLoanStateImpl(
      {this.currentStep = 1,
      this.step1Data = null,
      this.step2Data = false,
      this.step3Data = false,
      this.step4Data = false,
      this.step5Data = false,
      this.step6Data = false,
      this.step7Data = false,
      this.step8Data = false,
      this.step9Data = false,
      this.step10Data = false});

  @override
  @JsonKey()
  final int currentStep;
  @override
  @JsonKey()
  final ({
    String? backImagePath,
    DocumentType documentType,
    String frontImagePath
  })? step1Data;
  @override
  @JsonKey()
  final bool step2Data;
  @override
  @JsonKey()
  final bool step3Data;
  @override
  @JsonKey()
  final bool step4Data;
  @override
  @JsonKey()
  final bool step5Data;
  @override
  @JsonKey()
  final bool step6Data;
  @override
  @JsonKey()
  final bool step7Data;
  @override
  @JsonKey()
  final bool step8Data;
  @override
  @JsonKey()
  final bool step9Data;
  @override
  @JsonKey()
  final bool step10Data;

  @override
  String toString() {
    return 'CreateLoanState(currentStep: $currentStep, step1Data: $step1Data, step2Data: $step2Data, step3Data: $step3Data, step4Data: $step4Data, step5Data: $step5Data, step6Data: $step6Data, step7Data: $step7Data, step8Data: $step8Data, step9Data: $step9Data, step10Data: $step10Data)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateLoanStateImpl &&
            (identical(other.currentStep, currentStep) ||
                other.currentStep == currentStep) &&
            (identical(other.step1Data, step1Data) ||
                other.step1Data == step1Data) &&
            (identical(other.step2Data, step2Data) ||
                other.step2Data == step2Data) &&
            (identical(other.step3Data, step3Data) ||
                other.step3Data == step3Data) &&
            (identical(other.step4Data, step4Data) ||
                other.step4Data == step4Data) &&
            (identical(other.step5Data, step5Data) ||
                other.step5Data == step5Data) &&
            (identical(other.step6Data, step6Data) ||
                other.step6Data == step6Data) &&
            (identical(other.step7Data, step7Data) ||
                other.step7Data == step7Data) &&
            (identical(other.step8Data, step8Data) ||
                other.step8Data == step8Data) &&
            (identical(other.step9Data, step9Data) ||
                other.step9Data == step9Data) &&
            (identical(other.step10Data, step10Data) ||
                other.step10Data == step10Data));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      currentStep,
      step1Data,
      step2Data,
      step3Data,
      step4Data,
      step5Data,
      step6Data,
      step7Data,
      step8Data,
      step9Data,
      step10Data);

  /// Create a copy of CreateLoanState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateLoanStateImplCopyWith<_$CreateLoanStateImpl> get copyWith =>
      __$$CreateLoanStateImplCopyWithImpl<_$CreateLoanStateImpl>(
          this, _$identity);
}

abstract class _CreateLoanState implements CreateLoanState {
  const factory _CreateLoanState(
      {final int currentStep,
      final ({
        String? backImagePath,
        DocumentType documentType,
        String frontImagePath
      })? step1Data,
      final bool step2Data,
      final bool step3Data,
      final bool step4Data,
      final bool step5Data,
      final bool step6Data,
      final bool step7Data,
      final bool step8Data,
      final bool step9Data,
      final bool step10Data}) = _$CreateLoanStateImpl;

  @override
  int get currentStep;
  @override
  ({String? backImagePath, DocumentType documentType, String frontImagePath})?
      get step1Data;
  @override
  bool get step2Data;
  @override
  bool get step3Data;
  @override
  bool get step4Data;
  @override
  bool get step5Data;
  @override
  bool get step6Data;
  @override
  bool get step7Data;
  @override
  bool get step8Data;
  @override
  bool get step9Data;
  @override
  bool get step10Data;

  /// Create a copy of CreateLoanState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreateLoanStateImplCopyWith<_$CreateLoanStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
