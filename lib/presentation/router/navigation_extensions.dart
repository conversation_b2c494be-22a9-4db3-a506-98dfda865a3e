import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

// Route constants from core
import '../../core/router/app_routes.dart';
import '../../core/utils/app_logger.dart';

/// Extension methods for easy navigation
/// Located in presentation layer for Clean Architecture compliance
///
/// Clean Architecture compliance:
/// - Presentation layer can use Core layer utilities (app_routes, app_logger)
/// - Navigation logic stays in presentation layer
/// - No dependency violations
extension NavigationExtensions on BuildContext {
  // Auth Navigation Methods (Push - with back button)

  /// Push to select account type screen
  void goToSelectAccountType() {
    AppLogger.event('Navigation: Pushing to Select Account Type');
    push(AppRoutes.selectAccountType);
  }

  /// Push to personal info confirmation screen
  void goToPersonalInfoConfirmation({bool isCTVFlow = false}) {
    AppLogger.event('Navigation: Pushing to Personal Info Confirmation${isCTVFlow ? ' for CTV' : ''}');
    push(
      AppRoutes.personalInfoConfirmation,
      extra: isCTVFlow ? {'isCTVFlow': true} : null,
    );
  }

  /// Push to CTV policy screen
  void goToCtvPolicy() {
    AppLogger.event('Navigation: Pushing to CTV Policy');
    push(AppRoutes.ctvPolicy);
  }

  /// Push to KYC ID guide screen
  void goToKycIdGuide({bool isCTVFlow = false}) {
    AppLogger.event('Navigation: Pushing to KYC ID Guide${isCTVFlow ? ' for CTV' : ''}');
    push(
      AppRoutes.kycIdGuide,
      extra: isCTVFlow ? {
        'customTitle': 'Hướng dẫn xác thực giấy tờ',
        'customNotesText': 'Giấy tờ còn hạn sử dụng. Là hình gốc, không scan và photocopy./Chụp trong môi trường đủ ánh sáng./Đảm bảo ảnh rõ nét, không bị mờ loá.',
        'customAvoidText': 'Tránh sử dụng',
        'customButtonText': 'Tôi đã hiểu',
        'customWarningLabels': ['Không chụp quá mờ', 'Không mất góc', 'Không chụp loá sáng'],
      } : null,
    );
  }

  /// Push to registration success screen
  void goToRegistrationSuccess({
    bool isCTVFlow = false,
    String? fullName,
    String? branchName,
    String? position,
    String? referrerCode,
    String? referrerName,
  }) {
    AppLogger.event('Navigation: Pushing to Registration Success${isCTVFlow ? ' for CTV' : ''}');
    push(
      AppRoutes.registrationSuccess,
      extra: isCTVFlow ? {
        'customTitle': 'Giới thiệu CTV thành công',
        'customMessage': 'Kienlongbank sẽ liên hệ trong thời gian sớm nhất để thông báo kết quả',
        'customButtonText': 'Trang chủ',
        'fullName': fullName,
        'branchName': branchName,
        'position': position,
        'referrerCode': referrerCode,
        'referrerName': referrerName,
      } : null,
    );
  }

  // Document Navigation Methods (Push - with back button)

  /// Push to capture document screen
  void goToCaptureDocument() {
    AppLogger.event('Navigation: Pushing to Capture Document');
    push(AppRoutes.captureDocument);
  }

  /// Push to preview document screen
  void goToPreviewDocument() {
    AppLogger.event('Navigation: Pushing to Preview Document');
    push(AppRoutes.previewDocument);
  }

  /// Push preview document screen with parameters
  void pushPreviewDocumentWithParams({
    required dynamic image,
    required dynamic side,
    required dynamic onAccept,
  }) {
    AppLogger.event('Navigation: Pushing Preview Document with params');
    push(
      AppRoutes.previewDocument,
      extra: {
        'image': image,
        'side': side,
        'onAccept': onAccept,
      },
    );
  }

  // Identity Navigation Methods (Push - with back button)

  /// Push to identity upload screen
  void goToIdentityUpload({bool isCTVFlow = false}) {
    AppLogger.event('Navigation: Pushing to Identity Upload${isCTVFlow ? ' for CTV' : ''}');
    push(
      AppRoutes.identityUpload,
      extra: isCTVFlow ? {
        'customTitle': 'Giới thiệu CTV',
        'customDescription': 'Vui lòng chọn loại giấy tờ tùy thân để xác thực thông tin',
        'isCTVFlow': true,
      } : null,
    );
  }

  // Replace Navigation Methods (No back button)

  /// Navigate to login screen (replace current route)
  void goToLogin() {
    AppLogger.event('Navigation: Going to Login (replace)');
    go(AppRoutes.login);
  }

  /// Navigate to home screen (replace current route - for login flow)
  void goToHome() {
    AppLogger.event('Navigation: Going to Home (replace)');
    go(AppRoutes.home);
  }

  /// Replace with login (for logout or session expired)
  void replaceWithLogin() {
    AppLogger.event('Navigation: Replacing with Login');
    go(AppRoutes.login);
  }

  /// Replace with home (for successful login)
  void replaceWithHome() {
    AppLogger.event('Navigation: Replacing with Home');
    go(AppRoutes.home);
  }

  // Push Navigation Methods (Alternative - with back button)

  /// Push to home screen (for navigation from other screens)
  void pushToHome() {
    AppLogger.event('Navigation: Pushing to Home');
    push(AppRoutes.home);
  }

  // Demo Navigation

  /// Push to navigation demo screen
  void goToNavigationDemo() {
    AppLogger.event('Navigation: Pushing to Navigation Demo');
    push(AppRoutes.navigationDemo);
  }

  /// Push to UI demo screen
  void goToUiDemo() {
    AppLogger.event('Navigation: Pushing to UI Demo');
    push(AppRoutes.uiDemo);
  }

  // Utility Navigation Methods

  /// Go back to previous screen
  void goBack() {
    AppLogger.event('Navigation: Going back');
    if (canPop()) {
      pop();
    } else {
      // If can't pop, go to home or login
      goToLogin();
    }
  }

  /// Replace current route with new route (use go())
  void replaceWith(String route) {
    AppLogger.event('Navigation: Replacing with $route');
    go(route);
  }

  /// Clear navigation stack and go to route (use go())
  void clearAndGoTo(String route) {
    AppLogger.event('Navigation: Clearing stack and going to $route');
    go(route);
  }

  /// Push to QR scan screen
  void goToQRScan({bool isCTVFlow = false}) {
    AppLogger.event('Navigation: Pushing to QR Scan${isCTVFlow ? ' for CTV' : ''}');
    push(
      AppRoutes.qrScan,
      extra: isCTVFlow ? {'isCTVFlow': true} : null,
    );
  }
}
