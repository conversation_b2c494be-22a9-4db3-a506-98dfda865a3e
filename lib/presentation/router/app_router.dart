import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:sales_app/presentation/screens/home/<USER>';
import 'package:sales_app/presentation/screens/identity/qr_scan_screen.dart';

// Route constants from core
import '../../core/router/app_routes.dart';

// Presentation layer screens
import '../screens/auth/login_screen.dart';
import '../screens/auth/select_account_type_page.dart';
import '../screens/auth/personal_info_confirmation_screen.dart';
import '../screens/auth/cvt_policy_screen.dart';
import '../screens/auth/kyc_id_guide_screen.dart';
import '../screens/auth/registration_success_screen.dart';
import '../screens/document/capture_document_screen.dart';
import '../screens/document/preview_document_screen.dart';
import '../screens/identity/identity_upload_page.dart';

// Demo screens
import '../screens/demo/navigation_demo.dart';
import '../screens/demo/ui_demo_screen.dart';

// Core utilities
import '../../core/utils/app_logger.dart';

/// Router configuration provider
/// Located in presentation layer to avoid Clean Architecture violations
///
/// Clean Architecture compliance:
/// - Presentation layer can depend on Core layer (app_routes.dart)
/// - Presentation layer manages UI routing logic
/// - Core layer remains independent of UI concerns
final routerProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    initialLocation: AppRoutes.login,
    debugLogDiagnostics: true,
    routes: [
      // Auth Routes
      GoRoute(
        path: AppRoutes.login,
        name: AppRoutes.loginName,
        builder: (context, state) {
          AppLogger.info('Navigating to LoginScreen');
          return const LoginScreen();
        },
      ),

      GoRoute(
        path: AppRoutes.selectAccountType,
        name: AppRoutes.selectAccountTypeName,
        builder: (context, state) {
          AppLogger.info('Navigating to SelectAccountTypePage');
          return const SelectAccountTypePage();
        },
      ),

      GoRoute(
        path: AppRoutes.personalInfoConfirmation,
        name: AppRoutes.personalInfoConfirmationName,
        builder: (context, state) {
          AppLogger.info('Navigating to PersonalInfoConfirmationScreen');
          final extra = state.extra as Map<String, dynamic>?;
          return PersonalInfoConfirmationScreen(
            isCTVFlow: extra?['isCTVFlow'] ?? false,
          );
        },
      ),

      GoRoute(
        path: AppRoutes.ctvPolicy,
        name: AppRoutes.ctvPolicyName,
        builder: (context, state) {
          AppLogger.info('Navigating to CtvPolicyScreen');
          return const CTVPolicyScreen();
        },
      ),

      GoRoute(
        path: AppRoutes.kycIdGuide,
        name: AppRoutes.kycIdGuideName,
        builder: (context, state) {
          AppLogger.info('Navigating to KycIdGuideScreen');
          final extra = state.extra as Map<String, dynamic>?;
          return KycIdGuideScreen(
            customTitle: extra?['customTitle'],
            customNotesText: extra?['customNotesText'],
            customAvoidText: extra?['customAvoidText'],
            customButtonText: extra?['customButtonText'],
            customWarningLabels: extra?['customWarningLabels']?.cast<String>(),
          );
        },
      ),

      GoRoute(
        path: AppRoutes.registrationSuccess,
        name: AppRoutes.registrationSuccessName,
        builder: (context, state) {
          AppLogger.info('Navigating to RegistrationSuccessScreen');
          final extra = state.extra as Map<String, dynamic>?;
          return RegistrationSuccessScreen(
            customTitle: extra?['customTitle'],
            customMessage: extra?['customMessage'],
            customButtonText: extra?['customButtonText'],
            fullName: extra?['fullName'],
            branchName: extra?['branchName'],
            position: extra?['position'],
            referrerCode: extra?['referrerCode'],
            referrerName: extra?['referrerName'],
          );
        },
      ),

      // Main App Routes
      GoRoute(
        path: AppRoutes.home,
        name: AppRoutes.homeName,
        builder: (context, state) {
          AppLogger.info('Navigating to HomeScreen');
          return const HomeScreenV2();
        },
      ),

      // Document Routes with parameters
      GoRoute(
        path: AppRoutes.captureDocument,
        name: AppRoutes.captureDocumentName,
        builder: (context, state) {
          AppLogger.info('Navigating to CaptureDocumentScreen');
          final extra = state.extra as Map<String, dynamic>?;
          if (extra != null) {
            return CaptureDocumentScreen(
              title: extra['title'] ?? 'Chụp tài liệu',
              side: extra['side'],
              onImageCaptured: extra['onImageCaptured'],
            );
          }
          return const Scaffold(
            body: Center(child: Text('Invalid parameters')),
          );
        },
      ),

      GoRoute(
        path: AppRoutes.previewDocument,
        name: AppRoutes.previewDocumentName,
        builder: (context, state) {
          AppLogger.info('Navigating to PreviewDocumentScreen');
          final extra = state.extra as Map<String, dynamic>?;
          if (extra != null) {
            return PreviewDocumentScreen(
              image: extra['image'],
              side: extra['side'],
              onAccept: extra['onAccept'],
            );
          }
          return const Scaffold(
            body: Center(child: Text('Invalid parameters')),
          );
        },
      ),

      // Identity Routes
      GoRoute(
        path: AppRoutes.identityUpload,
        name: AppRoutes.identityUploadName,
        builder: (context, state) {
          AppLogger.info('Navigating to IdentityUploadPage');
          final extra = state.extra as Map<String, dynamic>?;
          return IdentityUploadPage(
            customTitle: extra?['customTitle'],
            customDescription: extra?['customDescription'],
            isCTVFlow: extra?['isCTVFlow'] ?? false,
          );
        },
      ),

      // Demo Routes
      GoRoute(
        path: AppRoutes.navigationDemo,
        name: AppRoutes.navigationDemoName,
        builder: (context, state) {
          AppLogger.info('Navigating to NavigationDemo');
          return const NavigationDemo();
        },
      ),

      GoRoute(
        path: AppRoutes.uiDemo,
        name: AppRoutes.uiDemoName,
        builder: (context, state) {
          AppLogger.info('Navigating to UiDemoScreen');
          return const UiDemoScreen();
        },
      ),
// qr scan
      GoRoute(
        path: AppRoutes.qrScan,
        name: AppRoutes.qrScanName,
        builder: (context, state) {
          AppLogger.info('Navigating to qr scan');
          final extra = state.extra as Map<String, dynamic>?;
          return QRScanScreen(
            isCTVFlow: extra?['isCTVFlow'] ?? false,
          );
        },
      ),
    ],
    errorBuilder: (context, state) {
      AppLogger.error('Navigation error: ${state.error}');
      return Scaffold(
        appBar: AppBar(title: const Text('Error')),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              Text('Route not found: ${state.uri}'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => context.go(AppRoutes.login),
                child: const Text('Go to Login'),
              ),
            ],
          ),
        ),
      );
    },
    redirect: (context, state) {
      // TODO: Add authentication logic here
      AppLogger.debug('Navigation redirect check for: ${state.uri}');
      return null;
    },
  );
});
