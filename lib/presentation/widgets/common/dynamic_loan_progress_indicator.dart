import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sales_app/core/enums/loan_step.dart';
import 'package:sales_app/presentation/utils/loan_step_calculator.dart';

/// Dynamic loan progress indicator that adapts to conditional steps
class DynamicLoanProgressIndicator extends StatelessWidget {
  final LoanStep currentStep;
  final List<LoanStep> availableSteps;
  final String stepTitle;
  final Color primaryColor;
  final Color backgroundColor;

  const DynamicLoanProgressIndicator({
    super.key,
    required this.currentStep,
    required this.availableSteps,
    required this.stepTitle,
    this.primaryColor = const Color(0xFF1E88E5),
    this.backgroundColor = Colors.white,
  });

  /// Get progress value (0.0 to 1.0)
  double get progress {
    return LoanStepCalculator.getProgress(currentStep, availableSteps);
  }

  /// Get current step number for display
  int get currentStepNumber {
    return LoanStepCalculator.getStepNumber(currentStep, availableSteps);
  }

  /// Get total steps count
  int get totalSteps {
    return LoanStepCalculator.getTotalSteps(availableSteps);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: backgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Progress bar section
          _buildProgressSection(),
          SizedBox(height: 16.h),
          // Step info section
          _buildStepInfoSection(),
        ],
      ),
    );
  }

  /// Build progress bar with percentage indicator
  Widget _buildProgressSection() {
    return Row(
      children: [
        // Linear progress bar
        Expanded(
          child: Container(
            height: 8.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4.r),
              color: Colors.grey[200],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(4.r),
              child: LinearProgressIndicator(
                value: progress,
                backgroundColor: Colors.transparent,
                valueColor: AlwaysStoppedAnimation<Color>(primaryColor),
                minHeight: 8.h,
              ),
            ),
          ),
        ),
        SizedBox(width: 16.w),
        // Percentage indicator
        _buildPercentageIndicator(),
      ],
    );
  }

  /// Build percentage indicator
  Widget _buildPercentageIndicator() {
    final percentage = (progress * 100).toInt();
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      decoration: BoxDecoration(
        color: primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Text(
        '$percentage%',
        style: TextStyle(
          fontSize: 14.sp,
          fontWeight: FontWeight.w600,
          color: primaryColor,
        ),
      ),
    );
  }

  /// Build step information section
  Widget _buildStepInfoSection() {
    return Row(
      children: [
        // Step title
        Expanded(
          child: Text(
            stepTitle,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
              color: Colors.grey[800],
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        SizedBox(width: 8.w),
        // Step counter
        _buildStepCounter(),
      ],
    );
  }

  /// Build step counter (e.g., "2 / 8")
  Widget _buildStepCounter() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: Colors.grey[300]!,
          width: 1,
        ),
      ),
      child: Text(
        '$currentStepNumber / $totalSteps',
        style: TextStyle(
          fontSize: 12.sp,
          fontWeight: FontWeight.w600,
          color: Colors.grey[700],
        ),
      ),
    );
  }
}

/// Extension to create DynamicLoanProgressIndicator with context
extension DynamicLoanProgressIndicatorExtension on DynamicLoanProgressIndicator {
  /// Create indicator with loan context
  static DynamicLoanProgressIndicator withContext({
    required LoanStep currentStep,
    required LoanContext context,
    required String stepTitle,
    Color primaryColor = const Color(0xFF1E88E5),
    Color backgroundColor = Colors.white,
  }) {
    final availableSteps = LoanStepCalculator.getAvailableSteps(context);
    
    return DynamicLoanProgressIndicator(
      currentStep: currentStep,
      availableSteps: availableSteps,
      stepTitle: stepTitle,
      primaryColor: primaryColor,
      backgroundColor: backgroundColor,
    );
  }
}
