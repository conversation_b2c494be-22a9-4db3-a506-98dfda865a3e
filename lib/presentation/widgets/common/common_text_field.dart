import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:sales_app/core/core.dart';
import 'package:sales_app/core/theme/app_color.dart';

class CommonTextField extends HookWidget {
  final String? label;
  final String? hintText;
  final String? helperText;
  final TextEditingController controller;
  final bool isSensitive;
  final bool allowClear;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final FormFieldValidator<String>? validator;
  final ValueChanged<String>? onChanged;
  final List<TextInputFormatter>? inputFormatters;
  final bool enabled;
  final bool isDropdown;
  final List<String>? items;
  final String? value;
  final Function(String?)? onDropdownChanged;
  final Color? backgroundColor;
  final Color? textColor;
  final Color? labelColor;
  final Color? iconColor;
  final Color? borderColor;
  final bool isDense;
  final bool filled;

  const CommonTextField({
    super.key,
    this.label,
    this.hintText,
    this.helperText,
    required this.controller,
    this.isSensitive = false,
    this.allowClear = false,
    this.prefixIcon,
    this.suffixIcon,
    this.keyboardType,
    this.textInputAction,
    this.validator,
    this.onChanged,
    this.inputFormatters,
    this.enabled = true,
    this.isDropdown = false,
    this.items,
    this.value,
    this.onDropdownChanged,
    this.backgroundColor,
    this.textColor,
    this.labelColor,
    this.iconColor,
    this.borderColor,
    this.isDense = false,
    this.filled = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isObscure = useState(isSensitive);
    final hasFocus = useState(false);
    final focusNode = useFocusNode();

    final defaultTextColor = textColor ?? AppColors.textPrimary;
    final defaultLabelColor = labelColor ?? AppColors.textSecondary;
    final defaultIconColor = iconColor ?? AppColors.primaryColor.withValues(alpha: 0.7);
    final defaultBorderColor = borderColor ?? AppColors.primaryColor;
    final borderRadius = BorderRadius.circular(8);

    // Listen to focus changes
    useEffect(() {
      void onFocusChange() {
        hasFocus.value = focusNode.hasFocus;
      }
      
      focusNode.addListener(onFocusChange);
      return () => focusNode.removeListener(onFocusChange);
    }, [focusNode]);

    // Xử lý suffix icons
    Widget? suffixWidget;
    if (suffixIcon != null) {
      suffixWidget = suffixIcon;
    } else {
      final List<Widget> suffixIcons = [];

      // Icon xóa text
      if (allowClear && controller.text.isNotEmpty) {
        suffixIcons.add(
          IconButton(
            icon: Icon(
              Icons.clear,
              size: 20,
              color: defaultIconColor,
            ),
            onPressed: () {
              controller.clear();
              if (onChanged != null) {
                onChanged!('');
              }
            },
          ),
        );
      }

      // Icon ẩn/hiện text nhạy cảm
      if (isSensitive) {
        suffixIcons.add(
          IconButton(
            icon: Icon(
              isObscure.value ? Icons.visibility : Icons.visibility_off,
              size: 20,
              color: defaultIconColor,
            ),
            onPressed: () => isObscure.value = !isObscure.value,
          ),
        );
      }

      if (suffixIcons.isNotEmpty) {
        suffixWidget = Row(
          mainAxisSize: MainAxisSize.min,
          children: suffixIcons,
        );
      }
    }

    return FormField<String>(
      validator: validator,
      builder: (formState) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              decoration: BoxDecoration(
                color: filled 
                    ? (backgroundColor ?? (hasFocus.value 
                        ? defaultBorderColor.withValues(alpha: 0.05)
                        : theme.colorScheme.surface))
                    : Colors.transparent,
                borderRadius: borderRadius,
                border: Border.all(
                  color: formState.hasError
                      ? theme.colorScheme.error
                      : (hasFocus.value 
                          ? defaultBorderColor
                          : defaultBorderColor.withValues(alpha: 0.2)),
                  width: hasFocus.value || formState.hasError ? 2 : 1,
                ),
              ),
              child: ClipRRect(
                borderRadius: borderRadius,
                child: isDropdown
                    ? DropdownButtonHideUnderline(
                        child: DropdownButton<String>(
                          value: value,
                          items: items?.map((item) => DropdownMenuItem(
                            value: item,
                            child: Text(
                              item,
                              style: theme.textTheme.bodyLarge?.copyWith(
                                color: defaultTextColor,
                                fontSize: 16,
                              ),
                            ),
                          )).toList(),
                          onChanged: enabled ? onDropdownChanged : null,
                          hint: Text(
                            hintText ?? '',
                            style: theme.textTheme.bodyLarge?.copyWith(
                              color: defaultLabelColor.withValues(alpha: 0.5),
                              fontSize: 16,
                            ),
                          ),
                          icon: Icon(
                            Icons.arrow_drop_down,
                            color: defaultIconColor,
                            size: 24,
                          ),
                          isExpanded: true,
                          dropdownColor: filled ? (backgroundColor ?? Colors.white) : Colors.transparent,
                        ),
                      )
                    : TextFormField(
                        controller: controller,
                        focusNode: focusNode,
                        obscureText: isSensitive && isObscure.value,
                        keyboardType: keyboardType,
                        textInputAction: textInputAction,
                        onChanged: (value) {
                          formState.didChange(value);
                          if (onChanged != null) {
                            onChanged!(value);
                          }
                        },
                        enabled: enabled,
                        inputFormatters: inputFormatters,
                        style: theme.textTheme.bodyLarge?.copyWith(
                          color: defaultTextColor,
                          fontSize: 16,
                        ),
                        decoration: InputDecoration(
                          isDense: true,
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: isDense ? 12 : 16,
                            vertical: isDense ? 8 : 12,
                          ),
                          filled: filled,
                          fillColor: Colors.transparent,
                          border: InputBorder.none,
                          enabledBorder: InputBorder.none,
                          focusedBorder: InputBorder.none,
                          errorBorder: InputBorder.none,
                          focusedErrorBorder: InputBorder.none,
                          errorStyle: const TextStyle(height: 0, fontSize: 0),
                          labelText: label,
                          labelStyle: TextStyle(
                            color: formState.hasError
                                ? theme.colorScheme.error
                                : (hasFocus.value 
                                    ? defaultBorderColor
                                    : defaultLabelColor),
                            fontSize: AppDimens.fontMD,
                          ),
                          hintText: hintText,
                          hintStyle: theme.textTheme.bodyLarge?.copyWith(
                            color: defaultLabelColor.withValues(alpha: 0.5),
                            fontSize: 16,
                          ),
                          prefixIcon: prefixIcon != null
                              ? IconTheme(
                                  data: IconThemeData(
                                    color: defaultIconColor,
                                    size: 20,
                                  ),
                                  child: prefixIcon!,
                                )
                              : null,
                          suffixIcon: suffixWidget,
                        ),
                      ),
              ),
            ),
            if (formState.hasError && formState.errorText != null) ...[
              Padding(
                padding: const EdgeInsets.only(top: 4),
                child: Text(
                  formState.errorText!,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.error,
                    height: 1.2,
                  ),
                ),
              ),
            ] else if (helperText != null) ...[
              Padding(
                padding: const EdgeInsets.only(top: 4),
                child: Text(
                  helperText!,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: defaultLabelColor,
                    height: 1.2,
                  ),
                ),
              ),
            ],
          ],
        );
      },
    );
  }
} 