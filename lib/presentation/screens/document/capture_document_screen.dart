import 'dart:io';
import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:sales_app/core/enums/document_side.dart';
import 'package:sales_app/core/constants/app_dimens.dart';
import 'package:sales_app/presentation/widgets/common/base_screen.dart';
import 'package:sales_app/core/utils/app_logger.dart';

import 'widgets/document_frame_painter.dart';

// Navigation imports (Clean Architecture compliant)
import '../../router/navigation_extensions.dart';

class CaptureDocumentScreen extends StatefulWidget {
  final String title;
  final DocumentSide side;
  final Function(File image) onImageCaptured;

  const CaptureDocumentScreen({
    super.key,
    required this.title,
    required this.side,
    required this.onImageCaptured,
  });

  @override
  State<CaptureDocumentScreen> createState() => _CaptureDocumentScreenState();
}

class _CaptureDocumentScreenState extends State<CaptureDocumentScreen> with WidgetsBindingObserver {
  CameraController? _controller;
  bool _isCameraInitialized = false;
  bool _isCapturing = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeCamera();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _controller?.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    final CameraController? cameraController = _controller;

    if (cameraController == null || !cameraController.value.isInitialized) {
      return;
    }

    if (state == AppLifecycleState.inactive) {
      cameraController.dispose();
    } else if (state == AppLifecycleState.resumed) {
      _initializeCamera();
    }
  }

  Future<void> _initializeCamera() async {
    try {
      final cameras = await availableCameras();
      if (cameras.isEmpty) return;

      _controller = CameraController(
        cameras.first,
        ResolutionPreset.high,
        enableAudio: false,
        imageFormatGroup: ImageFormatGroup.jpeg,
      );

      await _controller?.initialize();

      if (mounted) {
        setState(() {
          _isCameraInitialized = true;
        });
      }
    } catch (e) {
      AppLogger.error('Error initializing camera', error: e);
    }
  }

  Future<void> _captureImage() async {
    if (_controller == null || !_controller!.value.isInitialized || _isCapturing) {
      return;
    }

    try {
      setState(() {
        _isCapturing = true;
      });

      final xFile = await _controller!.takePicture();
      final file = File(xFile.path);

      if (mounted) {
        // Use Go Router navigation with parameters
        context.pushPreviewDocumentWithParams(
          image: file,
          side: widget.side,
          onAccept: widget.onImageCaptured,
        );
      }
    } catch (e) {
      AppLogger.error('Error capturing image', error: e);
    } finally {
      if (mounted) {
        setState(() {
          _isCapturing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    const cardRatio = AppDimens.cccdRatio; // Tỷ lệ CCCD
    final cardWidth = size.width * AppDimens.alphaNearOpaque;
    final cardHeight = cardWidth / cardRatio;
    final screenHeight = size.height - MediaQuery.of(context).padding.top - kToolbarHeight;
    final frameTop = (screenHeight - cardHeight) / AppDimens.scaleHuge; // Đưa khung lên cao hơn một chút

    return BaseScreen(
      title: widget.side == DocumentSide.front ? 'Chụp mặt trước' : 'Chụp mặt sau',
      body: Container(
        color: Colors.black,
        child: Stack(
          fit: StackFit.expand,
          children: [
            // Camera preview
            if (_isCameraInitialized && _controller != null)
              Transform.scale(
                scale: AppDimens.scaleLarge, // Tăng kích thước camera preview
                child: Center(
                  child: AspectRatio(
                    aspectRatio: _controller!.value.aspectRatio,
                    child: CameraPreview(_controller!),
                  ),
                ),
              ),

            // Full screen overlay
            Container(
              color: Colors.black,
            ),

            // Document frame overlay
            Positioned(
              top: frameTop,
              left: 0,
              right: 0,
              height: cardHeight,
              child: Stack(
                children: [
                  // Camera preview cho khung chụp
                  if (_isCameraInitialized && _controller != null)
                    ClipRect(
                      child: Transform.scale(
                        scale: AppDimens.scaleLarge, // Giữ tỷ lệ với camera preview chính
                        child: Center(
                          child: AspectRatio(
                            aspectRatio: _controller!.value.aspectRatio,
                            child: CameraPreview(_controller!),
                          ),
                        ),
                      ),
                    ),

                  // Khung chụp
                  CustomPaint(
                    painter: DocumentFramePainter(
                      width: cardWidth,
                      height: cardHeight,
                    ),
                    size: Size(size.width, cardHeight),
                  ),
                ],
              ),
            ),

            // Guidance text
            Positioned(
              top: frameTop + cardHeight + AppDimens.spacingXL,
              left: 0,
              right: 0,
              child: Container(
                margin: AppDimens.marginHorizontalLg,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'Vui lòng đặt giấy tờ vào trong khung để xác thực',
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    AppDimens.h16,
                    Text(
                      'Click để chụp ảnh',
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.white.withValues(alpha: AppDimens.alphaNearOpaque),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Capture button
            Positioned(
              left: 0,
              right: 0,
              bottom: AppDimens.containerMd,
              child: Center(
                child: _CaptureButton(
                  onTap: _captureImage,
                  isCapturing: _isCapturing,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _CaptureButton extends StatelessWidget {
  final VoidCallback onTap;
  final bool isCapturing;

  const _CaptureButton({
    required this.onTap,
    this.isCapturing = false,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final buttonSize = screenWidth < 360 ? AppDimens.captureButtonSize * 0.85 : AppDimens.captureButtonSize;
    final innerSize = screenWidth < 360 ? AppDimens.captureButtonInnerSize * 0.85 : AppDimens.captureButtonInnerSize;
    final innerSizePressed = screenWidth < 360 ? AppDimens.captureButtonInnerSizePressed * 0.85 : AppDimens.captureButtonInnerSizePressed;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: isCapturing ? null : onTap,
        borderRadius: BorderRadius.circular(buttonSize / 2),
        child: Container(
          width: buttonSize,
          height: buttonSize,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(
              color: Colors.white,
              width: AppDimens.borderWidthThick,
            ),
          ),
          child: Center(
            child: AnimatedContainer(
              duration: const Duration(milliseconds: AppDimens.animationFast),
              width: isCapturing ? innerSizePressed : innerSize,
              height: isCapturing ? innerSizePressed : innerSize,
              decoration: const BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
              ),
            ),
          ),
        ),
      ),
    );
  }
}