import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:sales_app/core/theme/app_color.dart';
import 'package:sales_app/core/constants/app_dimens.dart';
import 'package:sales_app/core/enums/document_type.dart';
import 'package:sales_app/presentation/widgets/common/common_dropdown.dart';
import 'package:sales_app/presentation/widgets/common/document_image_picker.dart';

class Step1IdentityDocumentScreen extends HookWidget {
  final Function({
    required DocumentType documentType,
    required String frontImagePath,
    String? backImagePath,
  }) onStepCompleted;

  const Step1IdentityDocumentScreen({
    super.key,
    required this.onStepCompleted,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final selectedDocType = useState<DocumentType>(DocumentType.cccd);
    final frontImagePath = useState<String?>(null);
    final backImagePath = useState<String?>(null);

    // <PERSON><PERSON><PERSON> tra điều kiện để gọi onStepCompleted
    void checkAndCallOnStepCompleted() {
      if (frontImagePath.value == null) return;
      
      if (selectedDocType.value == DocumentType.passport) {
        onStepCompleted(
          documentType: selectedDocType.value,
          frontImagePath: frontImagePath.value!,
          backImagePath: null,
        );
      } else if (backImagePath.value != null) {
        onStepCompleted(
          documentType: selectedDocType.value,
          frontImagePath: frontImagePath.value!,
          backImagePath: backImagePath.value,
        );
      }
    }

    return SafeArea(
      child: SingleChildScrollView(
        padding: AppDimens.paddingAllLg,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Vui lòng chọn loại giấy tờ tùy thân của người vay để xác thực thông tin',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            AppDimens.h24,
            CommonDropdown<DocumentType>(
              label: 'Loại giấy tờ',
              items: DocumentType.values,
              value: selectedDocType.value,
              isRequired: true,
              labelBuilder: (type) => type.label,
              onChanged: (type) {
                if (type != null) {
                  selectedDocType.value = type;
                  // Reset images when document type changes
                  frontImagePath.value = null;
                  backImagePath.value = null;
                }
              },
            ),
            AppDimens.h24,
            DocumentImagePicker(
              label: 'Mặt trước giấy tờ',
              imagePath: frontImagePath.value,
              onImageSelected: (path) {
                frontImagePath.value = path;
                checkAndCallOnStepCompleted();
              },
            ),
            AppDimens.h16,
            if (selectedDocType.value != DocumentType.passport)
              DocumentImagePicker(
                label: 'Mặt sau giấy tờ',
                imagePath: backImagePath.value,
                onImageSelected: (path) {
                  backImagePath.value = path;
                  checkAndCallOnStepCompleted();
                },
              ),
          ],
        ),
      ),
    );
  }
} 