import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:sales_app/core/constants/app_dimens.dart';
import 'package:sales_app/core/enums/document_type.dart';
import 'package:sales_app/core/enums/loan_step.dart';
import 'package:sales_app/presentation/widgets/common/loan_progress_indicator.dart';
import 'package:sales_app/presentation/widgets/common/bottom_button.dart';
import 'package:sales_app/presentation/widgets/common/base_screen.dart';
import 'package:sales_app/presentation/controllers/create_loan_controller.dart';
import 'package:sales_app/presentation/utils/loan_step_localizer.dart';
import 'package:sales_app/presentation/screens/loan/create_loan/steps/step1_identity_document_screen.dart';
import 'package:sales_app/presentation/screens/loan/create_loan/steps/step2_borrower_info_screen.dart';
import 'package:sales_app/presentation/screens/loan/create_loan/steps/step3_co_borrower_document_screen.dart';
import 'package:sales_app/presentation/screens/loan/create_loan/steps/step4_co_borrower_info_screen.dart';
import 'package:sales_app/presentation/screens/loan/create_loan/steps/step5_loan_request_screen.dart';
import 'package:sales_app/presentation/screens/loan/create_loan/steps/step6_financial_info_screen.dart';
import 'package:sales_app/presentation/screens/loan/create_loan/steps/step7_collateral_info_screen.dart';
import 'package:sales_app/presentation/screens/loan/create_loan/steps/step8_collateral_detail_screen.dart';
import 'package:sales_app/presentation/screens/loan/create_loan/steps/step9_document_list_screen.dart';
import 'package:sales_app/presentation/screens/loan/create_loan/steps/step10_loan_confirmation_screen.dart';
import 'package:sales_app/presentation/screens/loan/create_loan/steps/step11_success_screen.dart';

import '../../../../generated/l10n.dart';

class CreateLoanScreen extends HookConsumerWidget {
  const CreateLoanScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(createLoanControllerProvider);
    final controller = ref.read(createLoanControllerProvider.notifier);

    // Widget hiển thị nội dung của từng bước
    Widget buildStepContent() {
      switch (state.currentStep) {
        case LoanStep.identityDocument:
          return Step1IdentityDocumentScreen(
            onStepCompleted: ({
              required DocumentType documentType,
              required String frontImagePath,
              String? backImagePath,
            }) {
              controller.updateStep1Data(
                documentType: documentType,
                frontImagePath: frontImagePath,
                backImagePath: backImagePath,
              );
            },
          );
        case LoanStep.borrowerInfo:
          return Step2BorrowerInfoScreen(
            onStepCompleted: () => controller.updateStep2Data(true),
          );
        case LoanStep.coBorrowerDocument:
          return Step3CoBorrowerDocumentScreen(
            onStepCompleted: () => controller.updateStep3Data(true),
          );
        case LoanStep.coBorrowerInfo:
          return Step4CoBorrowerInfoScreen(
            onStepCompleted: () => controller.updateStep4Data(true),
          );
        case LoanStep.loanRequest:
          return Step5LoanRequestScreen(
            onStepCompleted: () => controller.updateStep5Data(true),
          );
        case LoanStep.financialInfo:
          return Step6FinancialInfoScreen(
            onStepCompleted: () => controller.updateStep6Data(true),
          );
        case LoanStep.collateralInfo:
          return Step7CollateralInfoScreen(
            onStepCompleted: () => controller.updateStep7Data(true),
          );
        case LoanStep.collateralDetail:
          return Step8CollateralDetailScreen(
            onStepCompleted: () => controller.updateStep8Data(true),
          );
        case LoanStep.documentList:
          return Step9DocumentListScreen(
            onStepCompleted: () => controller.updateStep9Data(true),
          );
        case LoanStep.loanConfirmation:
          return Step10LoanConfirmationScreen(
            onStepCompleted: () => controller.updateStep10Data(true),
          );
        case LoanStep.success:
          return const Step11SuccessScreen();
      }
    }

    return BaseScreen(
      title: S.of(context).createNewLoan,
      automaticallyImplyLeading: true,
      body: Column(
        children: [
          LoanProgressIndicator(
            currentStep: state.currentStep.stepNumber,
            totalSteps: CreateLoanController.totalSteps,
            stepTitle: LoanStepLocalizer.getStepTitle(context, state.currentStep),
          ),
          Expanded(
            child: buildStepContent(),
          ),
          if (!state.currentStep.isLast) // Không hiển thị nút tiếp tục ở bước cuối
            BottomButton(
              title: S.of(context).continueText,
              onPressed: controller.isContinueEnabled() ? controller.handleContinue : null,
            ),
        ],
      ),
    );
  }
}