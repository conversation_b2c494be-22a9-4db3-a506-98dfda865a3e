import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:sales_app/core/constants/app_dimens.dart';
import 'package:sales_app/core/enums/document_type.dart';
import 'package:sales_app/presentation/widgets/common/loan_progress_indicator.dart';
import 'package:sales_app/presentation/widgets/common/bottom_button.dart';
import 'package:sales_app/presentation/widgets/common/base_screen.dart';
import 'package:sales_app/presentation/controllers/create_loan_controller.dart';
import 'package:sales_app/presentation/screens/loan/create_loan/steps/step1_identity_document_screen.dart';
import 'package:sales_app/presentation/screens/loan/create_loan/steps/step2_borrower_info_screen.dart';
import 'package:sales_app/presentation/screens/loan/create_loan/steps/step3_co_borrower_document_screen.dart';
import 'package:sales_app/presentation/screens/loan/create_loan/steps/step4_co_borrower_info_screen.dart';
import 'package:sales_app/presentation/screens/loan/create_loan/steps/step5_loan_request_screen.dart';
import 'package:sales_app/presentation/screens/loan/create_loan/steps/step6_financial_info_screen.dart';
import 'package:sales_app/presentation/screens/loan/create_loan/steps/step7_collateral_info_screen.dart';
import 'package:sales_app/presentation/screens/loan/create_loan/steps/step8_collateral_detail_screen.dart';
import 'package:sales_app/presentation/screens/loan/create_loan/steps/step9_document_list_screen.dart';
import 'package:sales_app/presentation/screens/loan/create_loan/steps/step10_loan_confirmation_screen.dart';
import 'package:sales_app/presentation/screens/loan/create_loan/steps/step11_success_screen.dart';

class CreateLoanScreen extends HookConsumerWidget {
  const CreateLoanScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(createLoanControllerProvider);
    final controller = ref.read(createLoanControllerProvider.notifier);

    // Widget hiển thị nội dung của từng bước
    Widget buildStepContent() {
      switch (state.currentStep) {
        case 1:
          return Step1IdentityDocumentScreen(
            onStepCompleted: ({
              required DocumentType documentType,
              required String frontImagePath,
              String? backImagePath,
            }) {
              controller.updateStep1Data(
                documentType: documentType,
                frontImagePath: frontImagePath,
                backImagePath: backImagePath,
              );
            },
          );
        case 2:
          return Step2BorrowerInfoScreen(
            onStepCompleted: () => controller.updateStep2Data(true),
          );
        case 3:
          return Step3CoBorrowerDocumentScreen(
            onStepCompleted: () => controller.updateStep3Data(true),
          );
        case 4:
          return Step4CoBorrowerInfoScreen(
            onStepCompleted: () => controller.updateStep4Data(true),
          );
        case 5:
          return Step5LoanRequestScreen(
            onStepCompleted: () => controller.updateStep5Data(true),
          );
        case 6:
          return Step6FinancialInfoScreen(
            onStepCompleted: () => controller.updateStep6Data(true),
          );
        case 7:
          return Step7CollateralInfoScreen(
            onStepCompleted: () => controller.updateStep7Data(true),
          );
        case 8:
          return Step8CollateralDetailScreen(
            onStepCompleted: () => controller.updateStep8Data(true),
          );
        case 9:
          return Step9DocumentListScreen(
            onStepCompleted: () => controller.updateStep9Data(true),
          );
        case 10:
          return Step10LoanConfirmationScreen(
            onStepCompleted: () => controller.updateStep10Data(true),
          );
        case 11:
          return const Step11SuccessScreen();
        default:
          return const Center(child: Text('Đang phát triển...'));
      }
    }

    return BaseScreen(
      title: 'Tạo khoản vay mới',
      automaticallyImplyLeading: true,
      body: Column(
        children: [
          LoanProgressIndicator(
            currentStep: state.currentStep,
            totalSteps: CreateLoanController.totalSteps,
            stepTitle: CreateLoanController.stepTitles[state.currentStep] ?? '',
          ),
          Expanded(
            child: buildStepContent(),
          ),
          if (state.currentStep < 11) // Không hiển thị nút tiếp tục ở bước cuối
            BottomButton(
              title: 'Tiếp tục',
              onPressed: controller.isContinueEnabled() ? controller.handleContinue : null,
            ),
        ],
      ),
    );
  }
} 