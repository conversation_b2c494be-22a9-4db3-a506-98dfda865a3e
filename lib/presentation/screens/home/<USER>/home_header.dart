import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sales_app/core/enums/enums.dart';
import 'package:sales_app/presentation/router/router.dart';
import '../../../../core/theme/app_color.dart';
import '../../../../core/constants/app_dimens.dart';
import '../../../../domain/entities/user_entity.dart';
import '../../../widgets/common/common_text_field.dart';
import 'package:sales_app/presentation/screens/loan/create_loan/create_loan_screen.dart';

/// Widget Header cho trang chủ - hiển thị thông tin user, tìm kiếm và thao tác nhanh
class HomeHeader extends StatefulWidget {
  final UserEntity user;

  const HomeHeader({
    super.key,
    required this.user,
  });

  @override
  State<HomeHeader> createState() => _HomeHeaderState();
}

class _HomeHeaderState extends State<HomeHeader> {
  late final TextEditingController _searchController;

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  double _calculateHeaderHeight(BuildContext context) {
    // Calculate exact height needed - compact layout
    final statusBarHeight = MediaQuery.of(context).padding.top;
    const userInfoHeight = 50.0; // Giảm avatar + spacing
    const searchBarHeight = 48.0; // CommonTextField height
    const contentSpacing = 32.0; // Giảm spacing
    const padding = 24.0; // Giảm padding
    const quickActionsHeight = 80.0; // Giảm chiều cao QuickActions
    const quickActionsSpacing = 12.0; // Giảm khoảng cách

    return statusBarHeight +
        userInfoHeight +
        searchBarHeight +
        contentSpacing +
        padding +
        quickActionsHeight +
        quickActionsSpacing;
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: _calculateHeaderHeight(context),
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // Background gradient container
          Container(
            padding: EdgeInsets.only(
              top: MediaQuery.of(context).padding.top +
                  AppDimens.spacingLG, // Giảm top padding
              left: AppDimens.spacingLG,
              right: AppDimens.spacingLG,
              bottom: AppDimens.spacingLG, // Giảm bottom padding
            ),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppColors.primaryColor,
                  AppColors.secondaryColor,
                  AppColors.primaryColor.withValues(alpha: 0.9),
                ],
                stops: const [0.0, 0.6, 1.0],
              ),
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(32), // Tăng border radius
                bottomRight: Radius.circular(32),
              ),
              // Thêm subtle shadow cho depth
              boxShadow: [
                BoxShadow(
                  color: AppColors.primaryColor.withValues(alpha: 0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // User Info Row với Avatar, Greeting và Notifications
                Row(
                  children: [
                    // Avatar
                    GestureDetector(
                      onTap: () => _openProfile(context),
                      child: Container(
                        width: AppDimens.containerLg,
                        height: AppDimens.containerLg,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: const LinearGradient(
                            colors: [
                              AppColors.secondaryColor,
                              AppColors.buttonColor,
                            ],
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black
                                  .withValues(alpha: AppDimens.alphaMedium),
                              blurRadius: AppDimens.spacingSM,
                              offset: Offset(0, AppDimens.spacingXS),
                            ),
                          ],
                        ),
                        child: Icon(
                          Icons.person,
                          size: AppDimens.iconLG,
                          color: AppColors.textWhite,
                        ),
                      ),
                    ),

                    AppDimens.w16,

                    // User Info
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.user.getGreeting(),
                            style: TextStyle(
                              fontSize: AppDimens.fontMD,
                              color: AppColors.textWhite
                                  .withValues(alpha: AppDimens.alphaNearOpaque),
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                          Text(
                            widget.user.fullName,
                            style: TextStyle(
                              fontSize: AppDimens.fontXL,
                              fontWeight: FontWeight.bold,
                              color: AppColors.textWhite,
                            ),
                          ),
                          Text(
                            widget.user.role.displayName,
                            style: TextStyle(
                              fontSize: AppDimens.fontSM,
                              color: AppColors.textWhite
                                  .withValues(alpha: AppDimens.alphaVeryHigh),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Notification Bell
                    GestureDetector(
                      onTap: () => _openNotifications(context),
                      child: Container(
                        width: AppDimens.containerMd,
                        height: AppDimens.containerMd,
                        decoration: BoxDecoration(
                          color: AppColors.textWhite
                              .withValues(alpha: AppDimens.alphaMedium),
                          borderRadius: AppDimens.borderRadius12,
                        ),
                        child: Stack(
                          children: [
                            Center(
                              child: Icon(
                                Icons.notifications_outlined,
                                color: AppColors.textWhite,
                                size: AppDimens.iconMD,
                              ),
                            ),
                            if (widget.user.unreadNotifications > 0)
                              Positioned(
                                right: AppDimens.spacingSM,
                                top: AppDimens.spacingSM,
                                child: Container(
                                  padding: EdgeInsets.all(AppDimens.spacingXS),
                                  decoration: const BoxDecoration(
                                    color: AppColors.error,
                                    shape: BoxShape.circle,
                                  ),
                                  constraints: BoxConstraints(
                                    minWidth: AppDimens.badgeSize,
                                    minHeight: AppDimens.badgeSize,
                                  ),
                                  child: Text(
                                    widget.user.unreadNotifications >
                                            AppDimens.maxNotificationDisplay
                                        ? '${AppDimens.maxNotificationDisplay}+'
                                        : '${widget.user.unreadNotifications}',
                                    style: TextStyle(
                                      color: AppColors.textWhite,
                                      fontSize: AppDimens.fontXS,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),

                AppDimens.h32, // Giảm spacing để compact hơn

                // Search Bar
                _buildSearchBar(context),
              ],
            ),
          ),

          // Quick Actions positioned compact
          Positioned(
            bottom: -40, // Giảm khoảng cách để compact hơn
            left: AppDimens.spacingLG,
            right: AppDimens.spacingLG,
            child: _buildQuickActions(context),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.textWhite,
        borderRadius: AppDimens.borderRadius12,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: AppDimens.blurRadiusMedium,
            offset: AppDimens.offsetSmall,
          ),
        ],
      ),
      child: CommonTextField(
        controller: _searchController,
        hintText: 'Nhập số CCCD để tìm kiếm',
        prefixIcon: Icon(
          Icons.search,
          size: AppDimens.iconMD,
          color: AppColors.primaryColor.withValues(alpha: 0.7),
        ),
        allowClear: true,
        backgroundColor: Colors.transparent,
        borderColor: Colors.transparent,
        onChanged: (value) => _performSearch(context, value),
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context) {
    final actions = [
      QuickAction(
        icon: Icons.description_outlined,
        title: 'Tạo khoản\nvay',
        color: AppColors.info,
        onTap: () => _createLoan(context),
      ),
      QuickAction(
        icon: Icons.info_outline,
        title: 'Thông tin\nsản phẩm',
        color: AppColors.info,
        onTap: () => _viewProductInfo(context),
      ),
      QuickAction(
        icon: Icons.people_outline,
        title: 'Giới thiệu\nCTV',
        color: AppColors.info,
        onTap: () => context.goToKycIdGuide(isCTVFlow: true),
      ),
    ];

    return Container(
      // Container chính với enhanced styling
      decoration: BoxDecoration(
        color: AppColors.textWhite,
        borderRadius: AppDimens.borderRadius16,
        border: Border.all(
          color: AppColors.surfaceColor,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 16,
            offset: const Offset(0, 6),
          ),
          // Thêm subtle shadow để tạo depth
          BoxShadow(
            color: AppColors.primaryColor.withValues(alpha: 0.03),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: AppDimens.spacingSM,
          vertical:
              AppDimens.spacingXXS, // Giảm vertical padding để compact hơn
        ),
        child: Row(
          children:
              actions.map((action) => _buildCardActionItem(action)).toList(),
        ),
      ),
    );
  }

  Widget _buildCardActionItem(QuickAction action) {
    return Expanded(
      child: Container(
        // Container chính với padding mở rộng khoảng click
        margin: EdgeInsets.symmetric(horizontal: AppDimens.spacingXS),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () {
              // Haptic feedback khi tap
              HapticFeedback.lightImpact();
              action.onTap();
            },
            borderRadius: AppDimens.borderRadius12,
            splashColor: action.color.withValues(alpha: 0.1),
            highlightColor: action.color.withValues(alpha: 0.05),
            child: Container(
              // Container với 3 phần layout
              padding: EdgeInsets.all(
                  AppDimens.spacingSM), // Giảm padding để compact hơn
              decoration: BoxDecoration(
                borderRadius: AppDimens.borderRadius12,
                // Subtle border để tăng visual feedback
                border: Border.all(
                  color: Colors.transparent,
                  width: AppDimens.borderWidthThin,
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Phần 1: Icon container với enhanced styling
                  Container(
                    padding: EdgeInsets.all(AppDimens.spacingMD),
                    decoration: BoxDecoration(
                      color: action.color.withValues(alpha: 0.1),
                      borderRadius: AppDimens.borderRadius8,
                      // Thêm subtle shadow cho depth
                      boxShadow: [
                        BoxShadow(
                          color: action.color.withValues(alpha: 0.1),
                          blurRadius: AppDimens.spacingSM,
                          offset: Offset(0, AppDimens.spacingXXS),
                        ),
                      ],
                    ),
                    child: Icon(
                      action.icon,
                      color: action.color,
                      size: AppDimens.iconMD,
                    ),
                  ),

                  // Phần 2: Spacing
                  AppDimens.h8,

                  // Phần 3: Text với enhanced styling
                  Center(
                    child: Text(
                      action.title,
                      style: TextStyle(
                        fontSize: AppDimens.fontSM,
                        color: AppColors.textPrimary,
                        fontWeight: FontWeight.w500,
                        height: 1.3,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Navigation methods
  void _openProfile(BuildContext context) {
    // TODO: Navigate to profile screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Mở trang cá nhân')),
    );
  }

  void _openNotifications(BuildContext context) {
    // TODO: Navigate to notifications screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Mở thông báo')),
    );
  }

  void _performSearch(BuildContext context, String query) {
    // TODO: Implement search functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Tìm kiếm: $query')),
    );
  }

  void _createLoan(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CreateLoanScreen(),
      ),
    );
  }

  void _viewProductInfo(BuildContext context) {
    // TODO: Navigate to product info screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Xem thông tin sản phẩm')),
    );
  }

  void _showMoreActions(BuildContext context) {
    // TODO: Show bottom sheet with more actions
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Hiển thị thêm thao tác')),
    );
  }
}

/// Class đại diện cho một thao tác nhanh
class QuickAction {
  final IconData icon;
  final String title;
  final Color color;
  final VoidCallback onTap;

  const QuickAction({
    required this.icon,
    required this.title,
    required this.color,
    required this.onTap,
  });
}
