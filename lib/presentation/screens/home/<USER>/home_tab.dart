import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/theme/app_color.dart';
import '../../../../core/constants/app_dimens.dart';
import '../../../../domain/entities/user_entity.dart';
import '../widgets/home_header.dart';
import '../widgets/home_body.dart';

/// Tab trang chủ chính hiển thị Header + Body theo role người dùng
class HomeTab extends StatefulWidget {
  final UserEntity user;

  const HomeTab({
    super.key,
    required this.user,
  });

  @override
  State<HomeTab> createState() => _HomeTabState();
}

class _HomeTabState extends State<HomeTab>
    with AutomaticKeepAliveClientMixin {
  
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
        statusBarBrightness: Brightness.dark,
        systemNavigationBarColor: AppColors.backgroundColor,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
      child: Scaffold(
        backgroundColor: AppColors.backgroundColor,
        extendBodyBehindAppBar: true,
        body: RefreshIndicator(
          onRefresh: _refreshData,
          color: AppColors.primaryColor,
          child: CustomScrollView(
            slivers: [
              // Header Section - extends to status bar
              SliverToBoxAdapter(
                child: HomeHeader(user: widget.user),
              ),

              // Body Section - với spacing cho QuickActions overlap
              SliverToBoxAdapter(
                child: Padding(
                  padding: EdgeInsets.only(top: AppDimens.spacingXS), // Minimal spacing
                  child: HomeBody(user: widget.user),
                ),
              ),

              // Bottom safe area
              SliverToBoxAdapter(
                child: SafeArea(
                  top: false,
                  child: AppDimens.h16,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _refreshData() async {
    // Simulate refresh delay
    await Future.delayed(
      Duration(milliseconds: AppDimens.animationVerySlow),
    );
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Dữ liệu đã được cập nhật'),
          backgroundColor: AppColors.success,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: AppDimens.borderRadius8,
          ),
        ),
      );
    }
  }
} 