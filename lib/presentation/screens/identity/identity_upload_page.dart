import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:sales_app/core/constants/app_dimens.dart';
import 'package:sales_app/core/enums/document_type.dart';
import 'package:sales_app/core/theme/app_color.dart';
import 'package:sales_app/presentation/widgets/common/base_screen.dart';
import 'package:sales_app/presentation/widgets/common/bottom_button.dart';
import 'package:sales_app/presentation/widgets/common/common_dropdown.dart';
import 'package:sales_app/presentation/widgets/common/document_image_picker.dart';

// Navigation imports (Clean Architecture compliant)
import '../../router/navigation_extensions.dart';

class IdentityUploadPage extends HookWidget {
  final String? customTitle;
  final String? customDescription;
  final bool isCTVFlow;

  const IdentityUploadPage({
    super.key,
    this.customTitle,
    this.customDescription,
    this.isCTVFlow = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final selectedDocType = useState<DocumentType>(DocumentType.cccd);
    final frontImage = useState<String?>(null);
    final backImage = useState<String?>(null);

    void handleImagePick(bool isFront, String path) {
      if (isFront) {
        frontImage.value = path;
      } else {
        backImage.value = path;
      }
    }

    final isFormValid = frontImage.value != null && backImage.value != null;
    return BaseScreen(
      title: customTitle ?? (isCTVFlow ? 'Giới thiệu CTV' : 'Cung cấp giấy tờ tùy thân'),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: AppDimens.paddingAllMd,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Section title
                  if (isCTVFlow) ...[
                    Text(
                      'Cung cấp giấy tờ tuỳ thân',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    AppDimens.h8,
                  ],
                  Text(
                    customDescription ?? 'Vui lòng chọn loại giấy tờ tùy thân để xác thực thông tin',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontSize: 14,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  AppDimens.h24,
                  CommonDropdown<DocumentType>(
                    items: DocumentType.values,
                    value: selectedDocType.value,
                    label: 'Loại giấy tờ',
                    hint: 'Chọn loại giấy tờ',
                    isRequired: true,
                    onChanged: (doc) =>
                        selectedDocType.value = doc,
                    labelBuilder: (doc) => doc.label,
                    backgroundColor: AppColors.surfaceColor,
                    borderColor: AppColors.primaryColor.withValues(alpha: 0.2),
                  ),
                  AppDimens.h24,
                  DocumentImagePicker(
                    label: 'Ảnh mặt trước',
                    imagePath: frontImage.value,
                    onImageSelected: (path) => handleImagePick(true, path),
                  ),
                  AppDimens.h16,
                  DocumentImagePicker(
                    label: 'Ảnh mặt sau',
                    imagePath: backImage.value,
                    onImageSelected: (path) => handleImagePick(false, path),
                  ),
                ],
              ),
            ),
          ),
          BottomButton(
            title: 'Tiếp tục',
            onPressed: isFormValid
                ? () {
                    context.goToQRScan(isCTVFlow: isCTVFlow);
                  }
                : null,
            enabled: isFormValid,
          ),
        ],
      ),
    );
  }
}
