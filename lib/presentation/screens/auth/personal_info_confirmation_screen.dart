import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sales_app/core/theme/app_color.dart';
import 'package:sales_app/core/constants/app_dimens.dart';
import 'package:sales_app/generated/l10n.dart';
import '../../widgets/common/base_screen.dart';
import '../../widgets/common/common_text_field.dart';
import 'widgets/location_dropdown.dart';
import 'widgets/personal_info_bottom_buttons.dart';

// Navigation imports (Clean Architecture compliant)
import '../../router/navigation_extensions.dart';

class PersonalInfoConfirmationScreen extends StatefulWidget {
  final bool isCTVFlow;

  const PersonalInfoConfirmationScreen({
    super.key,
    this.isCTVFlow = false,
  });

  @override
  State<PersonalInfoConfirmationScreen> createState() =>
      _PersonalInfoConfirmationScreenState();
}

class _PersonalInfoConfirmationScreenState
    extends State<PersonalInfoConfirmationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  final _referralCodeController = TextEditingController();
  String? _selectedProvince;
  String? _selectedBranch;
  final bool _isLoading = false;

  // Sample data for extracted info
  final Map<String, dynamic> _extractedInfo = {
    'fullName': 'Nguyễn Văn A',
    'idNumber': '001234567890',
    'address': '123 Đường Nguyễn Huệ, Phường Bến Nghé, Quận 1, TP. Hồ Chí Minh',
  };

  // Mock data - In real app, this should come from API
  final List<String> _provinces = [
    'Hồ Chí Minh',
    'Hà Nội',
    'Đà Nẵng',
    'Cần Thơ',
  ];

  final Map<String, List<String>> _branches = {
    'Hồ Chí Minh': ['Chi nhánh Quận 1', 'Chi nhánh Quận 2'],
    'Hà Nội': ['Chi nhánh Cầu Giấy', 'Chi nhánh Đống Đa'],
    'Đà Nẵng': ['Chi nhánh Hải Châu'],
    'Cần Thơ': ['Chi nhánh Ninh Kiều'],
  };

  @override
  void dispose() {
    _phoneController.dispose();
    _referralCodeController.dispose();
    super.dispose();
  }

  String? _getBranchAddress() {
    if (_selectedProvince == null || _selectedBranch == null) return null;
    return '123 Đường ABC, $_selectedBranch, $_selectedProvince';
  }

  bool _isFormValid() {
    return _formKey.currentState?.validate() ?? false;
  }

  void _handleSubmit() {
    // Use Go Router navigation
    context.goToRegistrationSuccess(
      isCTVFlow: widget.isCTVFlow,
      fullName: _extractedInfo['fullName'],
      branchName: _selectedBranch,
      position: 'Cộng tác viên',
      referrerCode: _referralCodeController.text.isNotEmpty ? _referralCodeController.text : null,
      referrerName: _referralCodeController.text.isNotEmpty ? 'Nguyễn Thị L' : null, // Mock data
    );
  }

  @override
  Widget build(BuildContext context) {
    return BaseScreen(
      title: widget.isCTVFlow ? 'Giới thiệu CTV' : S.of(context).personalInfoConfirmation,
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.symmetric(
                    horizontal: AppDimens.spacingLG,
                    vertical: AppDimens.spacingMD),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildExtractedInfoSection(),
                    AppDimens.h24,
                    _buildUserInputSection(),
                  ],
                ),
              ),
            ),
            PersonalInfoBottomButtons(
              isLoading: _isLoading,
              isFormValid: _isFormValid(),
              onCancel: () => Navigator.of(context).pop(),
              onConfirm: _handleSubmit,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExtractedInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Row(
          children: [
            Icon(
              Icons.badge_outlined,
              size: 16,
              color: AppColors.primaryColor,
            ),
            SizedBox(width: 8),
            Text(
              'Thông tin từ giấy tờ tùy thân',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        CommonTextField(
          label: S.of(context).fullName,
          controller: TextEditingController(text: _extractedInfo['fullName']),
          enabled: false,
          backgroundColor: AppColors.surfaceColor,
          borderColor: AppColors.primaryColor.withValues(alpha: 0.2),
        ),
        const SizedBox(height: 8),
        CommonTextField(
          label: S.of(context).idNumber,
          controller: TextEditingController(text: _extractedInfo['idNumber']),
          enabled: false,
          backgroundColor: AppColors.surfaceColor,
          borderColor: AppColors.primaryColor.withValues(alpha: 0.2),
        ),
        const SizedBox(height: 8),
        CommonTextField(
          label: S.of(context).residentialAddress,
          controller: TextEditingController(text: _extractedInfo['address']),
          enabled: false,
          backgroundColor: AppColors.surfaceColor,
          borderColor: AppColors.primaryColor.withValues(alpha: 0.2),
        ),
      ],
    );
  }

  Widget _buildUserInputSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Row(
          children: [
            Icon(
              Icons.edit_outlined,
              size: 16,
              color: AppColors.primaryColor,
            ),
            SizedBox(width: 8),
            Text(
              'Thông tin bổ sung',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        CommonTextField(
          controller: _phoneController,
          label: S.of(context).phoneNumber,
          hintText: S.of(context).enterPhoneNumber,
          keyboardType: TextInputType.phone,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
            LengthLimitingTextInputFormatter(10),
          ],
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Vui lòng nhập số điện thoại';
            }
            if (!value.startsWith('0')) {
              return 'Số điện thoại phải bắt đầu bằng số 0';
            }
            if (value.length != 10) {
              return 'Số điện thoại phải có 10 chữ số';
            }
            return null;
          },
          isDense: true,
          backgroundColor: AppColors.backgroundColor,
          borderColor: AppColors.primaryColor.withValues(alpha: 0.2),
        ),
        const SizedBox(height: 8),
        CommonTextField(
          label: S.of(context).position,
          controller: TextEditingController(text: 'CTV đăng ký mới'),
          enabled: false,
          backgroundColor: AppColors.surfaceColor,
          borderColor: AppColors.primaryColor.withValues(alpha: 0.2),
        ),
        const SizedBox(height: 8),
        LocationDropdown(
          items: _provinces,
          value: _selectedProvince,
          label: S.of(context).province,
          hint: S.of(context).selectProvince,
          onChanged: (value) {
            setState(() {
              _selectedProvince = value;
              _selectedBranch = null;
            });
          },
          isDense: true,
        ),
        const SizedBox(height: 8),
        LocationDropdown(
          items: _selectedProvince != null ? _branches[_selectedProvince]! : [],
          value: _selectedBranch,
          label: S.of(context).branch,
          hint: S.of(context).selectBranch,
          onChanged: (value) {
            setState(() {
              _selectedBranch = value;
            });
          },
          isDense: true,
        ),
        const SizedBox(height: 8),
        CommonTextField(
          label: S.of(context).branchAddress,
          controller: TextEditingController(text: _getBranchAddress() ?? ''),
          enabled: false,
          backgroundColor: AppColors.backgroundColor,
          borderColor: AppColors.primaryColor.withValues(alpha: 0.2),
        ),
        const SizedBox(height: 8),
        CommonTextField(
          controller: _referralCodeController,
          label: S.of(context).referralCode,
          hintText: S.of(context).enterReferralCode,
          validator: (value) {
            if (value != null && value.isNotEmpty) {
              if (value.length < 6) {
                return 'Mã người giới thiệu không hợp lệ';
              }
            }
            return null;
          },
          isDense: true,
          backgroundColor: AppColors.backgroundColor,
          borderColor: AppColors.primaryColor.withValues(alpha: 0.2),
        ),
      ],
    );
  }
}
