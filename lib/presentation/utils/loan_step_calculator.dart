import 'package:sales_app/core/enums/loan_step.dart';

/// Context class to determine which steps are needed for the loan creation flow
class LoanContext {
  final bool needsBorrowerInfo;
  final bool hasCoBorrower;
  final bool needsCollateral;
  final bool needsFinancialInfo;
  final bool needsDocumentList;

  const LoanContext({
    this.needsBorrowerInfo = true,
    this.hasCoBorrower = false,
    this.needsCollateral = true,
    this.needsFinancialInfo = true,
    this.needsDocumentList = true,
  });

  /// Create context based on loan data
  factory LoanContext.fromLoanData({
    bool? hasCoBorrower,
    bool? needsCollateral,
    // Add more parameters as needed
  }) {
    return LoanContext(
      needsBorrowerInfo: true, // Always needed
      hasCoBorrower: hasCoBorrower ?? false,
      needsCollateral: needsCollateral ?? true,
      needsFinancialInfo: true, // Always needed
      needsDocumentList: true, // Always needed
    );
  }

  /// Default context for new loan (all steps included for now)
  factory LoanContext.defaultContext() {
    return const LoanContext(
      needsBorrowerInfo: true,
      hasCoBorrower: true, // Include co-borrower steps by default
      needsCollateral: true,
      needsFinancialInfo: true,
      needsDocumentList: true,
    );
  }
}

/// Calculator to determine available steps based on loan context
class LoanStepCalculator {
  /// Get list of available steps based on loan context
  static List<LoanStep> getAvailableSteps(LoanContext context) {
    List<LoanStep> steps = [];

    // Step 1: Identity document is always required
    steps.add(LoanStep.identityDocument);

    // Step 2: Borrower info (conditional)
    if (context.needsBorrowerInfo) {
      steps.add(LoanStep.borrowerInfo);
    }

    // Steps 3-4: Co-borrower steps (conditional)
    if (context.hasCoBorrower) {
      steps.add(LoanStep.coBorrowerDocument);
      steps.add(LoanStep.coBorrowerInfo);
    }

    // Step 5: Loan request is always required
    steps.add(LoanStep.loanRequest);

    // Step 6: Financial info (conditional)
    if (context.needsFinancialInfo) {
      steps.add(LoanStep.financialInfo);
    }

    // Steps 7-8: Collateral steps (conditional)
    if (context.needsCollateral) {
      steps.add(LoanStep.collateralInfo);
      steps.add(LoanStep.collateralDetail);
    }

    // Step 9: Document list (conditional)
    if (context.needsDocumentList) {
      steps.add(LoanStep.documentList);
    }

    // Step 10: Loan confirmation is always required
    steps.add(LoanStep.loanConfirmation);

    // Step 11: Success is always the final step
    steps.add(LoanStep.success);

    return steps;
  }

  /// Get current step index in available steps
  static int getCurrentStepIndex(LoanStep currentStep, List<LoanStep> availableSteps) {
    return availableSteps.indexOf(currentStep);
  }

  /// Get progress percentage (0.0 to 1.0)
  static double getProgress(LoanStep currentStep, List<LoanStep> availableSteps) {
    final currentIndex = getCurrentStepIndex(currentStep, availableSteps);
    if (currentIndex == -1 || availableSteps.isEmpty) return 0.0;
    return (currentIndex + 1) / availableSteps.length;
  }

  /// Get next available step
  static LoanStep? getNextStep(LoanStep currentStep, List<LoanStep> availableSteps) {
    final currentIndex = getCurrentStepIndex(currentStep, availableSteps);
    if (currentIndex == -1 || currentIndex >= availableSteps.length - 1) {
      return null;
    }
    return availableSteps[currentIndex + 1];
  }

  /// Get previous available step
  static LoanStep? getPreviousStep(LoanStep currentStep, List<LoanStep> availableSteps) {
    final currentIndex = getCurrentStepIndex(currentStep, availableSteps);
    if (currentIndex <= 0) return null;
    return availableSteps[currentIndex - 1];
  }

  /// Check if current step is the last step
  static bool isLastStep(LoanStep currentStep, List<LoanStep> availableSteps) {
    final currentIndex = getCurrentStepIndex(currentStep, availableSteps);
    return currentIndex == availableSteps.length - 1;
  }

  /// Check if current step is the first step
  static bool isFirstStep(LoanStep currentStep, List<LoanStep> availableSteps) {
    final currentIndex = getCurrentStepIndex(currentStep, availableSteps);
    return currentIndex == 0;
  }

  /// Get step number for display (1-based)
  static int getStepNumber(LoanStep currentStep, List<LoanStep> availableSteps) {
    final currentIndex = getCurrentStepIndex(currentStep, availableSteps);
    return currentIndex == -1 ? 1 : currentIndex + 1;
  }

  /// Get total number of steps
  static int getTotalSteps(List<LoanStep> availableSteps) {
    return availableSteps.length;
  }
}
