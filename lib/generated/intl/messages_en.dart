// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "additionalInfo":
            MessageLookupByLibrary.simpleMessage("Additional Information"),
        "ageRequirement": MessageLookupByLibrary.simpleMessage(
            "Age from 18 to 60, graduated from secondary school or higher"),
        "appName":
            MessageLookupByLibrary.simpleMessage("KienlongBank Sales App"),
        "areaKnowledge": MessageLookupByLibrary.simpleMessage(
            "Understanding of working area"),
        "assetRequirement": MessageLookupByLibrary.simpleMessage(
            "Minimum collateral of 150 million VND and commitment to comply with Kienlongbank\'s minimum average outstanding balance regulations"),
        "avoidUsingImages":
            MessageLookupByLibrary.simpleMessage("Avoid using images like:"),
        "bankWillContact": MessageLookupByLibrary.simpleMessage(
            "KienlongBank will contact you soon to inform the result!"),
        "benefitsAsCTV":
            MessageLookupByLibrary.simpleMessage("Benefits as CTV"),
        "branch": MessageLookupByLibrary.simpleMessage("Branch"),
        "branchAddress": MessageLookupByLibrary.simpleMessage("Branch Address"),
        "checkBackSide":
            MessageLookupByLibrary.simpleMessage("Check back side of ID"),
        "checkFrontSide":
            MessageLookupByLibrary.simpleMessage("Check front side of ID"),
        "clickToCapture":
            MessageLookupByLibrary.simpleMessage("Click to capture"),
        "close": MessageLookupByLibrary.simpleMessage("Close"),
        "confirm": MessageLookupByLibrary.simpleMessage("Confirm"),
        "continueText": MessageLookupByLibrary.simpleMessage("Continue"),
        "createNewLoan":
            MessageLookupByLibrary.simpleMessage("Create New Loan"),
        "dailyInstallmentLoan":
            MessageLookupByLibrary.simpleMessage("Daily Installment Loan"),
        "dailyInstallmentLoanDesc": MessageLookupByLibrary.simpleMessage(
            "Daily Installment Loan is a loan product in the form of daily installments (principal, interest) to serve living needs and business activities, other activities."),
        "documentVerificationGuide":
            MessageLookupByLibrary.simpleMessage("Document Verification Guide"),
        "enterPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Enter phone number"),
        "enterReferralCode":
            MessageLookupByLibrary.simpleMessage("Enter referral code"),
        "errorAuthAccessDenied": MessageLookupByLibrary.simpleMessage(
            "Access denied. You don\'t have permission to perform this action."),
        "errorAuthAccountLocked": MessageLookupByLibrary.simpleMessage(
            "Account is locked or disabled."),
        "errorAuthInvalidCredentials": MessageLookupByLibrary.simpleMessage(
            "Authentication failed. Please login again."),
        "errorAuthTokenExpired": MessageLookupByLibrary.simpleMessage(
            "Session expired. Please login again."),
        "errorAuthUnknown": MessageLookupByLibrary.simpleMessage(
            "Authentication error occurred."),
        "errorAuthUserNotFound":
            MessageLookupByLibrary.simpleMessage("User not found."),
        "errorCacheDataCorrupted":
            MessageLookupByLibrary.simpleMessage("Cached data is corrupted."),
        "errorCacheRead":
            MessageLookupByLibrary.simpleMessage("Failed to read cached data."),
        "errorCacheStorageFull":
            MessageLookupByLibrary.simpleMessage("Storage is full."),
        "errorCacheUnknown":
            MessageLookupByLibrary.simpleMessage("Cache error occurred."),
        "errorCacheWrite": MessageLookupByLibrary.simpleMessage(
            "Failed to save data to cache."),
        "errorNetworkCertificate": MessageLookupByLibrary.simpleMessage(
            "Certificate verification failed."),
        "errorNetworkConnectionTimeout": MessageLookupByLibrary.simpleMessage(
            "Connection timeout. Please check your internet connection."),
        "errorNetworkNoConnection": MessageLookupByLibrary.simpleMessage(
            "No internet connection. Please check your network settings."),
        "errorNetworkReceiveTimeout": MessageLookupByLibrary.simpleMessage(
            "Response timeout. Please try again."),
        "errorNetworkRequestCancelled":
            MessageLookupByLibrary.simpleMessage("Request was cancelled."),
        "errorNetworkSendTimeout": MessageLookupByLibrary.simpleMessage(
            "Request timeout. Please try again."),
        "errorNetworkUnknown": MessageLookupByLibrary.simpleMessage(
            "Network error. Please try again."),
        "errorServerBadRequest":
            MessageLookupByLibrary.simpleMessage("Invalid request data."),
        "errorServerInternal": MessageLookupByLibrary.simpleMessage(
            "Server error. Please try again later."),
        "errorServerNotFound": MessageLookupByLibrary.simpleMessage(
            "The requested resource was not found."),
        "errorServerTooManyRequests": MessageLookupByLibrary.simpleMessage(
            "Too many requests. Please try again later."),
        "errorServerUnknown": MessageLookupByLibrary.simpleMessage(
            "An error occurred. Please try again."),
        "errorValidationInvalidEmail": MessageLookupByLibrary.simpleMessage(
            "Please enter a valid email address."),
        "errorValidationInvalidFormat":
            MessageLookupByLibrary.simpleMessage("Invalid format."),
        "errorValidationInvalidPassword": MessageLookupByLibrary.simpleMessage(
            "Please enter a valid password."),
        "errorValidationRequiredField":
            MessageLookupByLibrary.simpleMessage("This field is required."),
        "errorValidationServer":
            MessageLookupByLibrary.simpleMessage("Validation failed."),
        "errorValidationTooLong":
            MessageLookupByLibrary.simpleMessage("Input is too long."),
        "errorValidationTooShort":
            MessageLookupByLibrary.simpleMessage("Input is too short."),
        "errorValidationUnknown":
            MessageLookupByLibrary.simpleMessage("Validation error occurred."),
        "flexibleWorkingHours":
            MessageLookupByLibrary.simpleMessage("Flexible working hours"),
        "fullName": MessageLookupByLibrary.simpleMessage("Full Name"),
        "goodBackground": MessageLookupByLibrary.simpleMessage(
            "Good background and character; No criminal record"),
        "healthInsurance": MessageLookupByLibrary.simpleMessage(
            "Health insurance and accident insurance provided by Kienlongbank when meeting outstanding balance conditions"),
        "healthRequirement":
            MessageLookupByLibrary.simpleMessage("Good health, clear mind"),
        "holidayBonus": MessageLookupByLibrary.simpleMessage(
            "Holiday bonus for 30/04, National Day 02/9, ..."),
        "home": MessageLookupByLibrary.simpleMessage("Home"),
        "idCard": MessageLookupByLibrary.simpleMessage("ID Card"),
        "idNumber": MessageLookupByLibrary.simpleMessage("ID Number"),
        "inDevelopment":
            MessageLookupByLibrary.simpleMessage("In Development..."),
        "infoFromId":
            MessageLookupByLibrary.simpleMessage("Information from ID"),
        "introduction": MessageLookupByLibrary.simpleMessage("Introduction"),
        "loanStep10Title":
            MessageLookupByLibrary.simpleMessage("Confirm Loan Information"),
        "loanStep11Title":
            MessageLookupByLibrary.simpleMessage("Loan Creation Successful"),
        "loanStep1Title":
            MessageLookupByLibrary.simpleMessage("Provide Identity Documents"),
        "loanStep2Title": MessageLookupByLibrary.simpleMessage(
            "Confirm Primary Borrower Information"),
        "loanStep3Title": MessageLookupByLibrary.simpleMessage(
            "Provide Identity Documents (Co-borrower)"),
        "loanStep4Title": MessageLookupByLibrary.simpleMessage(
            "Confirm Co-borrower Information"),
        "loanStep5Title": MessageLookupByLibrary.simpleMessage(
            "Provide Loan Request Information"),
        "loanStep6Title": MessageLookupByLibrary.simpleMessage(
            "Provide Financial Information"),
        "loanStep7Title": MessageLookupByLibrary.simpleMessage(
            "Provide Collateral Information"),
        "loanStep8Title": MessageLookupByLibrary.simpleMessage(
            "Detailed Collateral Information"),
        "loanStep9Title":
            MessageLookupByLibrary.simpleMessage("Provide Document List"),
        "login": MessageLookupByLibrary.simpleMessage("Login"),
        "monthlyServiceFee":
            MessageLookupByLibrary.simpleMessage("Monthly service fee"),
        "networkCheck": MessageLookupByLibrary.simpleMessage("Check"),
        "networkChecking":
            MessageLookupByLibrary.simpleMessage("Checking connection..."),
        "networkCheckingConnection":
            MessageLookupByLibrary.simpleMessage("Checking connection..."),
        "networkCheckingConnectionDescription":
            MessageLookupByLibrary.simpleMessage(
                "Checking connection status..."),
        "networkConnected": MessageLookupByLibrary.simpleMessage("Connected"),
        "networkContinue": MessageLookupByLibrary.simpleMessage("Continue"),
        "networkDisconnected":
            MessageLookupByLibrary.simpleMessage("No internet connection"),
        "networkDisconnectedDescription": MessageLookupByLibrary.simpleMessage(
            "Please check your internet connection and try again"),
        "networkReconnectedSuccess":
            MessageLookupByLibrary.simpleMessage("Successfully reconnected"),
        "networkRetry": MessageLookupByLibrary.simpleMessage("Retry"),
        "networkRetryChecking":
            MessageLookupByLibrary.simpleMessage("Checking..."),
        "networkUnstable":
            MessageLookupByLibrary.simpleMessage("Unstable connection"),
        "networkUnstableConnectionDescription":
            MessageLookupByLibrary.simpleMessage(
                "Network connection is unstable. Some features may be affected"),
        "networkUnstableDescription": MessageLookupByLibrary.simpleMessage(
            "Network is unstable - Some features may be affected"),
        "networkUnstableWarning":
            MessageLookupByLibrary.simpleMessage("Unstable connection"),
        "newCTV": MessageLookupByLibrary.simpleMessage("New CTV"),
        "noOtherBank": MessageLookupByLibrary.simpleMessage(
            "Not working for other credit institutions or finance companies"),
        "notesWhenTakingDocuments": MessageLookupByLibrary.simpleMessage(
            "Notes when taking documents:"),
        "passport": MessageLookupByLibrary.simpleMessage("Passport"),
        "password": MessageLookupByLibrary.simpleMessage("Password"),
        "personalInfoConfirmation": MessageLookupByLibrary.simpleMessage(
            "Personal Information Confirmation"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("Phone Number"),
        "placeDocumentInFrame": MessageLookupByLibrary.simpleMessage(
            "Please place the document in the frame for verification"),
        "pleaseCheckPhoto":
            MessageLookupByLibrary.simpleMessage("Please check the photo"),
        "position": MessageLookupByLibrary.simpleMessage("Position"),
        "province": MessageLookupByLibrary.simpleMessage("Province/City"),
        "referralCode":
            MessageLookupByLibrary.simpleMessage("Referral Code (if any)"),
        "register": MessageLookupByLibrary.simpleMessage("Register"),
        "register_account":
            MessageLookupByLibrary.simpleMessage("Register account"),
        "registrationSuccess":
            MessageLookupByLibrary.simpleMessage("Registration Successful"),
        "requirements": MessageLookupByLibrary.simpleMessage("Requirements:"),
        "residentialAddress":
            MessageLookupByLibrary.simpleMessage("Residential Address"),
        "retake": MessageLookupByLibrary.simpleMessage("Retake"),
        "selectBranch": MessageLookupByLibrary.simpleMessage("Select branch"),
        "selectProvince":
            MessageLookupByLibrary.simpleMessage("Select province/city"),
        "stepsToBecomeCTV":
            MessageLookupByLibrary.simpleMessage("Steps to become a CTV"),
        "suitableFor": MessageLookupByLibrary.simpleMessage(
            "Suitable for individuals with sudden, short-term spending needs; small businesses and individual businesses; customers with stable income and good credit history."),
        "unlimitedIncome":
            MessageLookupByLibrary.simpleMessage("Unlimited income"),
        "vietnameseCitizen": MessageLookupByLibrary.simpleMessage(
            "Vietnamese citizen with legal capacity, civil act capacity and civil liability according to law"),
        "workUniform":
            MessageLookupByLibrary.simpleMessage("Work uniform provided")
      };
}
