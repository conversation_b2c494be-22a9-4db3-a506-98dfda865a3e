import 'package:flutter/material.dart';
import 'package:sales_app/generated/l10n.dart';

/// Enum representing loan creation steps with localization support
enum LoanStep {
  identityDocument(1),
  borrowerInfo(2),
  coBorrowerDocument(3),
  coBorrowerInfo(4),
  loanRequest(5),
  financialInfo(6),
  collateralInfo(7),
  collateralDetail(8),
  documentList(9),
  loanConfirmation(10),
  success(11);

  const LoanStep(this.stepNumber);

  final int stepNumber;

  /// Get localized title for this step
  String getTitle(BuildContext context) {
    final s = S.of(context);
    switch (this) {
      case LoanStep.identityDocument:
        return s.loanStep1Title;
      case LoanStep.borrowerInfo:
        return s.loanStep2Title;
      case LoanStep.coBorrowerDocument:
        return s.loanStep3Title;
      case LoanStep.coBorrowerInfo:
        return s.loanStep4Title;
      case LoanStep.loanRequest:
        return s.loanStep5Title;
      case LoanStep.financialInfo:
        return s.loanStep6Title;
      case LoanStep.collateralInfo:
        return s.loanStep7Title;
      case LoanStep.collateralDetail:
        return s.loanStep8Title;
      case LoanStep.documentList:
        return s.loanStep9Title;
      case LoanStep.loanConfirmation:
        return s.loanStep10Title;
      case LoanStep.success:
        return s.loanStep11Title;
    }
  }

  /// Get step by number
  static LoanStep fromNumber(int number) {
    return values.firstWhere(
      (step) => step.stepNumber == number,
      orElse: () => LoanStep.identityDocument,
    );
  }

  /// Get next step
  LoanStep? get next {
    final currentIndex = values.indexOf(this);
    if (currentIndex < values.length - 1) {
      return values[currentIndex + 1];
    }
    return null;
  }

  /// Get previous step
  LoanStep? get previous {
    final currentIndex = values.indexOf(this);
    if (currentIndex > 0) {
      return values[currentIndex - 1];
    }
    return null;
  }

  /// Check if this is the last step
  bool get isLast => this == LoanStep.success;

  /// Check if this is the first step
  bool get isFirst => this == LoanStep.identityDocument;

  /// Get total number of steps
  static int get totalSteps => values.length;
}
