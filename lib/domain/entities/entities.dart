/// Domain Entities Barrel Export
/// Business entities representing core domain objects
/// 
/// This file provides a single import point for all domain entities.
/// Entities represent the core business objects and contain business logic.
/// 
/// Usage:
/// ```dart
/// import 'package:sales_app/domain/entities/entities.dart';
/// 
/// // Now you can use any entity:
/// final user = User(
///   id: '123',
///   name: '<PERSON>',
///   email: '<EMAIL>',
/// );
/// 
/// final environment = EnvironmentEntity(
///   name: 'Development',
///   baseUrl: 'https://dev-api.example.com',
/// );
/// ```
/// 
/// ## Entity Categories:
/// - **User Entities**: User domain objects and related entities
/// - **Environment Entities**: Environment configuration entities
library entities;

export 'environment_entity.dart';
export 'user.dart';
export 'user_entity.dart';
