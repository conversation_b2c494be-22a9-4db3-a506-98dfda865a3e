import '../../core/enums/user_role.dart';

/// Entity quản lý thông tin người dùng trong hệ thống App Sale
class UserEntity {
  final String id;
  final String fullName;
  final String? avatarUrl;
  final UserRole role;
  final String phoneNumber;
  final String email;
  final int unreadNotifications;
  final DateTime? lastLoginAt;
  final bool isActive;

  const UserEntity({
    required this.id,
    required this.fullName,
    this.avatarUrl,
    required this.role,
    required this.phoneNumber,
    required this.email,
    this.unreadNotifications = 0,
    this.lastLoginAt,
    this.isActive = true,
  });

  /// Tạo bản sao của UserEntity với các thuộc tính được cập nhật
  UserEntity copyWith({
    String? id,
    String? fullName,
    String? avatarUrl,
    UserRole? role,
    String? phoneNumber,
    String? email,
    int? unreadNotifications,
    DateTime? lastLoginAt,
    bool? isActive,
  }) {
    return UserEntity(
      id: id ?? this.id,
      fullName: fullName ?? this.fullName,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      role: role ?? this.role,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      unreadNotifications: unreadNotifications ?? this.unreadNotifications,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      isActive: isActive ?? this.isActive,
    );
  }

  /// Tạo UserEntity rỗng để sử dụng làm giá trị mặc định
  factory UserEntity.empty() {
    return const UserEntity(
      id: '',
      fullName: '',
      role: UserRole.ctv,
      phoneNumber: '',
      email: '',
    );
  }

  /// Lấy tên hiển thị ngắn gọn (tên cuối)
  String get shortName {
    final names = fullName.trim().split(' ');
    return names.isNotEmpty ? names.last : fullName;
  }

  /// Lấy lời chào theo thời gian trong ngày
  String getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'Chào buổi sáng 👋';
    } else if (hour < 17) {
      return 'Chào buổi chiều 👋';
    } else {
      return 'Chào buổi tối 👋';
    }
  }
} 